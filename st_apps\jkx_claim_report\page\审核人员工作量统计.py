import datetime as dt
import warnings

import pandas as pd
import streamlit as st
from pandasql import sqldf
from streamlit_date_picker import date_range_picker, PickerType

from utils.auth import agent_auth_check
from utils.st import query_sql, text_write, text_new, sub_text_write
from utils.utils import sum_or_combine ,replace_using_dict
from utils.date import get_workdays_count

warnings.filterwarnings("ignore")
pd.set_option("display.max_columns", None)
pd.set_option("display.expand_frame_repr", False)
pd.set_option("display.float_format", lambda x: "%.2f" % x)


CONNECTOR = st.connection("claim", type="sql")
CONNECTOR_CLAIM = st.connection("claim_nhb", type="sql")
CONNECTOR_JKX = st.connection("jkx", type="sql")
CONNECTOR_YZ = st.connection('claim_yangzi', type='sql')
CONNECTOR_GZ = st.connection('claim_guizhou', type='sql')
CONNECTOR_DW = st.connection("dw", type="sql")


def get_product_code():
    """
    获取产品代码、金额等信息
    :return:
    """
    SQL_PRODUCT_CODE = """
    SELECT REPLACE
        ( REPLACE ( p.NAME, '基础版', '' ), '升级版', '' ) product_name,
        REPLACE ( ps.NAME, '南京宁惠保', '' ) product_short_name,
        ps.NAME product_set_name,
        ps.code product_set_code,
        GROUP_CONCAT( p.CODE SEPARATOR ',' ) product_code,
        valid_from,'ninghuibao' product_serial_code,'南京宁惠保' product_serial_name
    FROM
        product p
        JOIN product_set ps ON p.product_set_id = ps.id 
    WHERE
        ps.CODE LIKE 'ninghuibao%' 
    GROUP BY
        ps.NAME 
    ORDER BY
        ps.CODE DESC
    """
    SQL_YZ = """
    SELECT
    	product_set_code,
    	GROUP_CONCAT( product_code SEPARATOR "," ) product_code ,
    	case product_set_code when 'boxianyuan' then '玻纤院'
			when 'qingjiang' then '淮安清江石化'
			when 'taizhou' then '泰州石化'
			when 'yangzi' then '扬子石化' else product_set_code end as product_set_name
            ,'yangzi' product_serial_code,'扬子' product_serial_name
    FROM
    	(
    	SELECT
    		name product_name,
    		CODE product_code ,
    		SUBSTRING_INDEX(CODE, '-', 1) product_set_code
    	FROM
    		product 
    	WHERE
    	CODE like 'yangzi%'
    	or CODE like 'boxianyuan%'
        or code like 'taizhou%'
			or code like 'qingjiang%') t 
    GROUP BY
    	product_set_code
    """

    SQL_GZ = """
    SELECT
        p.NAME product_name,
        ps.NAME product_short_name,
        ps.NAME product_set_name,
        ps.code product_set_code,
        GROUP_CONCAT( p.CODE SEPARATOR ',' ) product_code,
        p.valid_from,
        'guihuibao' product_serial_code ,
        '贵州惠民保' product_serial_name
    FROM
        product p
        join jkx_ds_slave.product pp on p.code = pp.code
        JOIN jkx_ds_slave.product_set ps ON pp.product_set_id = ps.id 
    GROUP BY
        product_name 
    ORDER BY
        valid_from DESC
    """

    SQL_CLAIM = """
    SELECT REPLACE
        ( REPLACE ( p.NAME, '基础版', '' ), '升级版', '' ) product_name,
        REPLACE ( ps.NAME, pss.name, '' ) product_short_name,
        ps.NAME product_set_name,
        ps.code product_set_code,
        GROUP_CONCAT( p.CODE SEPARATOR ',' ) product_code,
        valid_from,pss.`name` product_serial_name,
        ps.product_serial_code
        
    FROM
        product p
        JOIN product_set ps ON p.product_set_code = ps.CODE 
        JOIN product_serial pss ON ps.product_serial_code = pss.CODE 
    WHERE
        pss.`code` not in ('binzhou_yhb','dezhou_hmb','rizhao_nxb')
    GROUP BY
        ps.NAME 
    ORDER BY
        ps.CODE DESC
        """

    df_product_code = CONNECTOR_JKX.query(
        SQL_PRODUCT_CODE, show_spinner="查询中...", ttl=60
    )
    df_product_code_yz = CONNECTOR_YZ.query(
        SQL_YZ, show_spinner="查询中...", ttl=60
    )
    df_product_code_gz = CONNECTOR_GZ.query(
        SQL_GZ, show_spinner="查询中...", ttl=60
    )
    # 贵州对一期名称特殊处理
    df_product_code_gz['product_short_name'] = df_product_code_gz['product_short_name'].apply(
        lambda x: '一期' if x == '贵州惠民保' else x.replace('贵州惠民保', ''))
    df_product_code_gz['product_set_name'] = df_product_code_gz['product_set_name'].apply(
    lambda x: '贵州惠民保一期' if x == '贵州惠民保' else x)

    df_product_code_claim = CONNECTOR.query(SQL_CLAIM, show_spinner="查询中...", ttl=60)

    # 合并数据
    df_product_code = pd.concat([df_product_code, df_product_code_gz])
    df_product_code = pd.concat([df_product_code, df_product_code_yz])
    df_product_code = pd.concat([df_product_code, df_product_code_claim])
    df_product_code.reset_index(drop=True, inplace=True)
    df_product_code["product_code_list"] = df_product_code["product_code"].apply(
        lambda x: x.split(",")
    )
    # 将product_code中的字符串用单引号包裹
    df_product_code["product_code"] = df_product_code["product_code_list"].apply(
        lambda x: ",".join([f"'{i}'" for i in x])
    )
    df_product_code.drop(columns=["product_code_list"], inplace=True)
    
    # 根据product_serial_code分组后进行shift操作
    df_product_code["product_code_shift"] = df_product_code.groupby("product_serial_code")["product_code"].shift(-1)
    df_product_code["product_name_shift"] = df_product_code.groupby("product_serial_code")["product_name"].shift(-1)
    return df_product_code


def get_auditor_workload_target():
    """
    获取审核人员工作量目标
    :param product_serial_code: 产品系列代码
    :return:df
    """
    SQL_AUDITOR_WORKLOAD_TARGET = """
    SELECT
        product_serial_code,   
        name,
        is_review,
        workload_target
    from 
        other_audit_person_target
        """
    df = CONNECTOR_DW.query(
        SQL_AUDITOR_WORKLOAD_TARGET, show_spinner="查询中...", ttl=60
    )
    product_serial_name_map = {
        "ninghuibao": "南京宁惠保",
        "yangzi": "扬子"
    }
    df['product_serial_name'] = df['product_serial_code'].map(product_serial_name_map)
    if df.empty:
        df = pd.DataFrame(columns=['product_serial_name','product_serial_code','name','is_review','workload_target'])
    return df



def create_daily_summary_table(df_daily, selected_cases, target_value=420, title="日度明细"):
    """
    创建带合计行和样式的日度明细表格

    参数:
    - df_daily: 日度数据DataFrame
    - selected_cases: 选中的审核人员列表
    - target_value: 指标值，默认420
    - title: 表格标题，默认"日度明细"

    返回:
    - 无返回值，直接在Streamlit中显示表格
    """
    # 筛选选中的审核人员数据
    selected_daily = df_daily[df_daily['审核人员'].isin(selected_cases)]
    if not selected_daily.empty:
        sub_text_write(title)
        text_new("由于一个案件在周期内只计算一次，所以日度合计不一定等于汇总统计")

        # 计算总计列
        selected_daily['工作量'] = selected_daily['工作量'].astype(int)
        # 设置指标值
        selected_daily['指标'] = target_value

        # 计算达成率数值，保留用于样式判断
        selected_daily['达成率_数值'] = selected_daily.apply(
            lambda x: round(x['工作量'] / x['指标'] * 100, 2), axis=1
        )

        # 排序
        selected_daily.sort_values(by=['审核人员', '审核日期'], inplace=True)

        # 格式化达成率显示
        selected_daily['达成率'] = selected_daily['达成率_数值'].apply(lambda x: f"{x}%")

        # 选择显示列
        display_columns = ['审核日期', '审核人员', '指标', '工作量', '达成率']
        selected_daily_display = selected_daily[display_columns].copy()

        # 确保所有列都是字符串类型，避免类型冲突
        for col in display_columns:
            selected_daily_display[col] = selected_daily_display[col].astype(str)

        # 添加合计行
        if len(selected_daily_display) > 0:
            total_target = selected_daily_display['指标'].astype(int).sum()
            total_workload = selected_daily_display['工作量'].astype(int).sum()
            total_achievement_rate = round(total_workload / total_target * 100, 2) if total_target > 0 else 0

            # 创建合计行
            total_row = pd.DataFrame({
                '审核日期': ['合计'],
                '审核人员': [''],
                '指标': [str(total_target)],
                '工作量': [str(total_workload)],
                '达成率': [f"{total_achievement_rate}%"]
            })

            # 将合计行添加到显示数据框
            selected_daily_display = pd.concat([selected_daily_display, total_row], ignore_index=True)

        # 创建达成率数值映射表，解决索引不一致问题
        achievement_rate_map = {}
        for idx in selected_daily_display.index:
            if idx < len(selected_daily_display) - 1:  # 普通行
                # 通过审核人员和审核日期匹配原始数据
                auditor = selected_daily_display.loc[idx, '审核人员']
                audit_date = selected_daily_display.loc[idx, '审核日期']

                # 在原始数据中查找匹配的行（确保数据类型一致）
                matching_rows = selected_daily[
                    (selected_daily['审核人员'] == auditor) &
                    (selected_daily['审核日期'].astype(str) == audit_date)
                ]

                if not matching_rows.empty:
                    achievement_rate_map[idx] = matching_rows.iloc[0]['达成率_数值']
                else:
                    achievement_rate_map[idx] = 100
            else:  # 合计行
                achievement_str = selected_daily_display.loc[idx, '达成率']
                if achievement_str and achievement_str != '':
                    achievement_rate_map[idx] = float(achievement_str.replace('%', ''))
                else:
                    achievement_rate_map[idx] = 100

        # 定义样式函数，为达成率低于100%的单元格添加淡黄色背景
        def highlight_low_achievement(row):
            # 获取对应行的达成率数值
            achievement_rate = achievement_rate_map.get(row.name, 100)

            styles = [''] * len(row)

            # 如果达成率低于100%，为达成率列添加淡黄色背景
            if achievement_rate < 100:
                achievement_col_idx = display_columns.index('达成率')
                styles[achievement_col_idx] = 'background-color: #FFFACD'

            # 为合计行添加特殊样式（加粗背景）
            if row.name == len(selected_daily_display) - 1:  # 合计行
                styles = ['background-color: #F0F0F0; font-weight: bold'] * len(row)
                # 如果合计行达成率低于100%，保持淡黄色背景
                if achievement_rate < 100:
                    achievement_col_idx = display_columns.index('达成率')
                    styles[achievement_col_idx] = 'background-color: #FFFACD; font-weight: bold'

            return styles

        # 应用样式并显示
        styled_df = selected_daily_display.style.apply(highlight_low_achievement, axis=1)
        st.dataframe(styled_df, hide_index=True, use_container_width=True)



def get_auditor_workload(product_set_code,start_datetime,end_datetime,name_standard,conn=CONNECTOR_CLAIM,sql=None):
    """
    获取审核人员工作量
    :param product_set_code: 产品集代码
    :param start_datetime: 开始时间
    :param end_datetime: 结束时间
    :param conn: 数据库连接
    :return:
    """
    if sql is None:
        sql = [query_sql("SQL_AUDITOR_WORKLOAD_V1"), query_sql("SQL_AUDITOR_DAILY_WORKLOAD_V1")]
    df = conn.query(
        sql[0].format(
            product_set_code=product_set_code,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
        ), show_spinner="查询中...", ttl=60)
    df_daily = conn.query(
        sql[1].format(
            product_set_code=product_set_code,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
        ), show_spinner="查询中...", ttl=60)
    df['product_serial_name'] = '南京宁惠保'
    df = df[pd.to_numeric(df['points_value'], errors='coerce').notna()]
    df['total_workload'] = df.apply(lambda x: float(x['points_value'])*float(x['claim_count']),axis=1)
    df.rename(columns={'total_workload': '工作量','fullname':'审核人员','points_value':'积分值','claim_count':'数量',
    'points_status':'案件状态','product_serial_name':'项目名称'}, inplace=True)

    df_product = get_product_code()
    # df 中 产品集名称 根据df_product进行匹配，通过product_set_code进行匹配
    product_name_map = dict(zip(df_product['product_set_code'], df_product['product_set_name']))
    # 添加产品集名称列
    df['产品集名称'] = df['product_set_code'].map(product_name_map)
    df = df[['项目名称','产品集名称','审核人员','案件状态','数量','积分值','工作量']]
    df['数量'] = df['数量'].astype(float)
    if name_standard == '是':
        df['审核人员'] = df['审核人员'].apply(lambda x: x[:-1] if x[-1] in ['初', '复'] else x)
    df['积分值'] = df['积分值'].astype(float)
    if df.empty:
        df = pd.DataFrame(columns=['项目名称','产品集名称','审核人员','案件状态','数量','积分值','工作量'])
    # 日度统计
    df_daily['product_serial_name'] = '南京宁惠保'
    df_daily['total_workload'] = df_daily.apply(lambda x: float(x['points_value']) * float(x['claim_count']),
                                                axis=1)
    df_daily.rename(columns={'total_workload': '工作量', 'fullname': '审核人员', 'points_value': '积分值',
                             'claim_count': '数量','points_status': '案件状态', 'product_serial_name': '项目名称',
                             'create_date': '审核日期'}, inplace=True)
    df_daily['产品集名称'] = df_daily['product_set_code'].map(product_name_map)
    df_daily = df_daily[['审核日期', '项目名称', '产品集名称', '审核人员', '案件状态', '数量', '积分值', '工作量']]
    df_daily['数量'] = df_daily['数量'].astype(float)
    if name_standard == '是':
        df_daily['审核人员'] = df_daily['审核人员'].apply(lambda x: x[:-1] if x[-1] in ['初', '复'] else x)
    df_daily['积分值'] = df_daily['积分值'].astype(float)
    if df_daily.empty:
        df_daily = pd.DataFrame(
            columns=['审核日期', '项目名称', '产品集名称', '审核人员', '案件状态', '数量', '积分值','工作量'])

    return df, df_daily


def get_auditor_workload_gz(product_set_code,start_datetime,end_datetime,name_standard,conn=CONNECTOR_GZ,sql=None):
    """
    获取审核人员工作量
    :param product_set_code: 产品集代码
    :param start_datetime: 开始时间
    :param end_datetime: 结束时间
    :param conn: 数据库连接
    :return:
    """
    if sql is None:
        sql = [query_sql("SQL_AUDITOR_WORKLOAD_GZ"), query_sql("SQL_AUDITOR_DAILY_WORKLOAD_GZ")]
    df = conn.query(
        sql[0].format(
            product_set_code=product_set_code,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
        ), show_spinner="查询中...", ttl=60)
    df_daily = conn.query(
        sql[1].format(
            product_set_code=product_set_code,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
        ), show_spinner="查询中...", ttl=60)
    # 如果points_value转不成数字，则删除本行
    df = df[pd.to_numeric(df['points_value'], errors='coerce').notna()]

    df['total_workload'] = df.apply(lambda x: float(x['points_value'])*float(x['claim_count']),axis=1)
    df.rename(columns={'total_workload': '工作量','fullname':'审核人员','points_value':'积分值','claim_count':'数量',
    'points_status':'案件状态','product_serial_name':'项目名称'}, inplace=True)

    df_product = get_product_code()
    # df 中 产品集名称 根据df_product进行匹配，通过product_set_code进行匹配
    product_name_map = dict(zip(df_product['product_set_code'], df_product['product_set_name']))
    # 添加产品集名称列
    df['产品集名称'] = df['product_set_code'].map(product_name_map)
    if name_standard == '是':
        df['审核人员'] = df['审核人员'].apply(lambda x: x[:-1] if x[-1] in ['初', '复'] else x)
    df = df[['项目名称','产品集名称','审核人员','案件状态','数量','积分值','工作量']]
    if df.empty:
        df = pd.DataFrame(columns=['项目名称','产品集名称','审核人员','案件状态','数量','积分值','工作量'])

    # 日度统计数据
    df_daily = df_daily[pd.to_numeric(df_daily['points_value'], errors='coerce').notna()]

    df_daily['total_workload'] = df_daily.apply(lambda x: float(x['points_value']) * float(x['claim_count']),
                                                axis=1)
    df_daily.rename(columns={'total_workload': '工作量', 'fullname': '审核人员', 'points_value': '积分值',
                             'claim_count': '数量', 'create_date': '审核日期',
                             'points_status': '案件状态', 'product_serial_name': '项目名称'}, inplace=True)

    # 添加产品集名称列
    df_daily['产品集名称'] = df_daily['product_set_code'].map(product_name_map)
    if name_standard == '是':
        df_daily['审核人员'] = df_daily['审核人员'].apply(lambda x: x[:-1] if x[-1] in ['初', '复'] else x)
    df_daily = df_daily[
        ['审核日期', '项目名称', '产品集名称', '审核人员', '案件状态', '数量', '积分值', '工作量']]
    if df_daily.empty:
        df_daily = pd.DataFrame(
            columns=['审核日期', '项目名称', '产品集名称', '审核人员', '案件状态', '数量', '积分值','工作量'])
    return df, df_daily


def get_auditor_workload_yz(product_code,start_datetime,end_datetime,name_standard,conn=CONNECTOR_YZ,sql=None):
    """
    获取扬子审核人员工作量
    :param product_code: 产品代码
    :param start_datetime: 开始时间
    :param end_datetime: 结束时间
    :param conn: 数据库连接
    :return:
    """
    if sql is None:
        sql = [query_sql("SQL_AUDITOR_WORKLOAD_YZ"), query_sql("SQL_AUDITOR_DAILY_WORKLOAD_YZ")]
    df = conn.query(
        sql[0].format(
            product_code=product_code,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
        ), show_spinner="查询中...", ttl=60)
    df_daily = conn.query(
        sql[1].format(
            product_code=product_code,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
        ), show_spinner="查询中...", ttl=60)
    df['product_serial_name'] = '扬子'
    df = df[pd.to_numeric(df['points_value'], errors='coerce').notna()]
    df['total_workload'] = df.apply(lambda x: float(x['points_value'])*float(x['claim_count']),axis=1)
    df.rename(columns={'total_workload': '工作量','fullname':'审核人员','points_value':'积分值','claim_count':'数量',
    'points_status':'案件状态','product_serial_name':'项目名称'}, inplace=True)
    df_product = get_product_code()
    # df 中 产品集名称 根据df_product进行匹配，通过product_set_code进行匹配
    product_name_map = dict(zip(df_product['product_set_code'], df_product['product_set_name']))
    # 添加产品集名称列
    df['产品集名称'] = df['product_set_code'].map(product_name_map)
    df = df[['项目名称','产品集名称','审核人员','案件状态','数量','积分值','工作量']]
    df['积分值'] = df['积分值'].astype(float)
    df['数量'] = df['数量'].astype(float)
    if name_standard == '是':
        df['审核人员'] = df['审核人员'].apply(lambda x: x[:-1] if x[-1] in ['初', '复'] else x)

    ######################################特殊处理##############################################
    # 获取审核人员工作量目标数据，用于判断是否为复审人员
    df_auditor_workload_target = get_auditor_workload_target()
    # 重命名列名，将product_serial_name改为项目名称，name改为审核人员，便于后续数据合并
    df_auditor_workload_target.rename(columns={'product_serial_name': '项目名称', 'name': '审核人员'}, inplace=True)
    # 将工作量目标数据与当前数据进行合并，通过项目名称和审核人员进行关联
    df = pd.merge(df,df_auditor_workload_target,on=['项目名称','审核人员'],how='left')
    
    # 如果审核人员是复审人员(is_review=1)，则将其案件状态从A改为B
    df.loc[(df['is_review']==1)&(df['案件状态']=='A'),'案件状态'] = 'B'
    # 删除不需要的列(is_review和workload_target)
    df.drop(columns=['is_review','workload_target'],inplace=True)
    ######################################特殊处理##############################################


    df['案件状态'] = df['案件状态'].apply(lambda x: replace_using_dict(x,{'A':'A.初审（每张发票）',
                                                                        'B':'B.复审（每张发票）'}))
    if df.empty:
        df = pd.DataFrame(columns=['项目名称','产品集名称','审核人员','案件状态','数量','积分值','工作量'])
    # 日度统计
    df_daily['product_serial_name'] = '扬子'
    df_daily['total_workload'] = df_daily.apply(lambda x: float(x['points_value']) * float(x['claim_count']),
                                                axis=1)
    df_daily.rename(columns={'total_workload': '工作量', 'fullname': '审核人员', 'points_value': '积分值',
                             'claim_count': '数量', 'create_date': '审核日期',
                             'points_status': '案件状态', 'product_serial_name': '项目名称'}, inplace=True)

    # 添加产品集名称列
    df_daily['产品集名称'] = df_daily['product_set_code'].map(product_name_map)
    df_daily = df_daily[
        ['审核日期', '项目名称', '产品集名称', '审核人员', '案件状态', '数量', '积分值', '工作量']]
    df_daily['积分值'] = df_daily['积分值'].astype(float)
    df_daily['数量'] = df_daily['数量'].astype(float)
    if name_standard == '是':
        df_daily['审核人员'] = df_daily['审核人员'].apply(lambda x: x[:-1] if x[-1] in ['初', '复'] else x)
    # 特殊处理
    # 将工作量目标数据与当前数据进行合并，通过项目名称和审核人员进行关联
    df_daily = pd.merge(df_daily, df_auditor_workload_target, on=['项目名称', '审核人员'], how='left')

    # 如果审核人员是复审人员(is_review=1)，则将其案件状态从A改为B
    df_daily.loc[(df_daily['is_review'] == 1) & (df_daily['案件状态'] == 'A'), '案件状态'] = 'B'
    # 删除不需要的列(is_review和workload_target)
    df_daily.drop(columns=['is_review', 'workload_target'], inplace=True)
    if df_daily.empty:
        df_daily = pd.DataFrame(
            columns=['审核日期', '项目名称', '产品集名称', '审核人员', '案件状态', '数量', '积分值','工作量'])
    return df, df_daily

def get_auditor_workload_v3(product_set_code,start_datetime,end_datetime,name_standard,conn=CONNECTOR,sql=None):
    """
    获取审核人员工作量
    :param product_set_code: 产品集代码
    :param start_datetime: 开始时间
    :param end_datetime: 结束时间
    :param conn: 数据库连接
    :return:
    """
    if sql is None:
        sql = [query_sql("SQL_AUDITOR_WORKLOAD_JS"),query_sql("SQL_AUDITOR_DAILY_WORKLOAD_JS")]
    product_set_code_list = product_set_code.replace("'",'').split(',')
    # 只获取其中以jiangsu_yhb开头的产品集代码
    if sql == 'SQL_AUDITOR_WORKLOAD_V3_1':
        sql = [query_sql("SQL_AUDITOR_WORKLOAD_V3_1"),query_sql("SQL_AUDITOR_DAILY_WORKLOAD_V3_1")]
        product_set_code_list = [i for i in product_set_code_list if i.startswith(('shanxi_jkb', 'neimenggu_hmb'))]
    elif sql == 'SQL_AUDITOR_WORKLOAD_JS':
        sql = [query_sql("SQL_AUDITOR_WORKLOAD_JS"),query_sql("SQL_AUDITOR_DAILY_WORKLOAD_JS")]
        product_set_code_list = [i for i in product_set_code_list if i.startswith('jiangsu_yhb')]
    elif sql == 'SQL_AUDITOR_WORKLOAD_V3_2':
        sql = [query_sql("SQL_AUDITOR_WORKLOAD_V3_2"),query_sql("SQL_AUDITOR_DAILY_WORKLOAD_V3_2")]
        product_set_code_list = [i for i in product_set_code_list if i.startswith(('xiantao_xhb', 'hunan_amb'))]
    elif sql == 'SQL_AUDITOR_WORKLOAD_V3_YC':
        sql = [query_sql("SQL_AUDITOR_WORKLOAD_V3_YC"),query_sql("SQL_AUDITOR_DAILY_WORKLOAD_V3_YC")]
        product_set_code_list = [i for i in product_set_code_list if i.startswith('yichun_hmb')]
    else:
        sql = [query_sql("SQL_AUDITOR_WORKLOAD_V3_JJ"),query_sql("SQL_AUDITOR_DAILY_WORKLOAD_V3_JJ")]
        product_set_code_list = [i for i in product_set_code_list if i.startswith('jiujiang_hxb')]

    if not product_set_code_list:
        return pd.DataFrame(columns=['项目名称','产品集名称','审核人员','案件状态','数量','积分值','工作量']),pd.DataFrame(columns=['审核日期','项目名称', '产品集名称', '审核人员', '案件状态', '数量', '积分值', '工作量'])

    product_set_code = "','".join(product_set_code_list)

    
    df = conn.query(
        sql[0].format(
            product_set_code=product_set_code,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
        ),show_spinner="查询中...",ttl=60)
    df_daily = conn.query(
        sql[1].format(
            product_set_code=product_set_code,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
        ), show_spinner="查询中...", ttl=60)
    df = df[pd.to_numeric(df['points_value'], errors='coerce').notna()]
    df_daily = df_daily[pd.to_numeric(df_daily['points_value'], errors='coerce').notna()]

    df['total_workload'] = df.apply(lambda x: float(x['points_value'])*float(x['claim_count']),axis=1)
    df.rename(columns={'total_workload': '工作量','fullname':'审核人员','points_value':'积分值','claim_count':'理赔件数',
    'points_status':'案件状态','product_serial_name':'项目名称'}, inplace=True)

    df_product = get_product_code()
    # df 中 产品集名称 根据df_product进行匹配，通过product_set_code进行匹配
    product_name_map = dict(zip(df_product['product_set_code'], df_product['product_set_name']))
    # 添加产品集名称列
    df['产品集名称'] = df['product_set_code'].map(product_name_map)
    if name_standard == '是':
        df['审核人员'] = df['审核人员'].apply(lambda x: x[:-1] if x[-1] in ['初', '复'] else x)
        df['审核人员'] = df['审核人员'].apply(lambda x: x.replace('（医惠保）', '') if x.endswith('（医惠保）') else x)
    df = df[['项目名称','产品集名称','审核人员','案件状态','理赔件数','积分值','工作量']]
    if df.empty:
        df = pd.DataFrame(columns=['项目名称','产品集名称','审核人员','案件状态','数量','积分值','工作量'])
    # 日度统计
    df_daily['total_workload'] = df_daily.apply(lambda x: float(x['points_value']) * float(x['claim_count']),
                                                axis=1)
    df_daily.rename(columns={'total_workload': '工作量', 'fullname': '审核人员', 'points_value': '积分值',
                             'claim_count': '数量', 'create_date': '审核日期',
                             'points_status': '案件状态', 'product_serial_name': '项目名称'}, inplace=True)

    # 添加产品集名称列
    df_daily['产品集名称'] = df_daily['product_set_code'].map(product_name_map)
    if name_standard == '是':
        df_daily['审核人员'] = df_daily['审核人员'].apply(lambda x: x[:-1] if x[-1] in ['初', '复'] else x)
        df_daily['审核人员'] = df_daily['审核人员'].apply(lambda x: x.replace('（医惠保）', '') if x.endswith('（医惠保）') else x)
    df_daily = df_daily[
        ['审核日期', '项目名称', '产品集名称', '审核人员', '案件状态', '数量', '积分值', '工作量']]
    if df_daily.empty:
        df_daily = pd.DataFrame(
            columns=['审核日期', '项目名称', '产品集名称', '审核人员', '案件状态', '数量', '积分值','工作量'])
    return df,df_daily



def process_workload_df(df, product_type, days):
        """处理工作量DataFrame的通用函数"""
        if df.empty:
            return None
            
        # Pivot and fill NA
        df = df.pivot_table(
            index=['项目名称', '审核人员'],
            columns='案件状态',
            values='工作量',
            aggfunc='sum'
        ).reset_index().fillna(0)
        
        # Calculate totals
        numeric_cols = df.select_dtypes(include=['float64', 'int64']).columns
        df['总计'] = df[numeric_cols].sum(axis=1)
        df['指标'] = 420 * days
        df['达成率'] = (df['总计'] / df['指标'] * 100).round(2)
        df.sort_values(by=['达成率'], ascending=False, inplace=True)
        df['达成率'] = df['达成率'].apply(lambda x: f"{x}%")
        
        # Product-specific column ordering
        if product_type == "summary":
            return df[['项目名称', '审核人员', '指标', '总计', '达成率']]
        else:
            front_cols = ['项目名称', '审核人员', '指标']
            end_cols = ['总计', '达成率']
            middle_cols = [col for col in df.columns if col not in front_cols + end_cols]
            return df[front_cols + middle_cols + end_cols]


def main():
    st.subheader("审核人员工作量统计")
    is_iframe, user_permission, disable, product_serial_code_iframe, product_set_code_iframe = agent_auth_check()
    product_info = get_product_code()
    if product_serial_code_iframe in product_info['product_serial_code'].values or product_serial_code_iframe is None:
        if is_iframe == 1 and product_serial_code_iframe is not None:
            iframe_product_serial_name = \
            product_info[product_info['product_serial_code'] == product_serial_code_iframe][
                'product_serial_name'].values[0]
            product_serial_name_input = st.multiselect('产品系列', product_info['product_serial_name'].tolist(),
                                                 default=[iframe_product_serial_name],disabled=True)
        else:
            product_serial_name_input = st.multiselect(
                "请选择产品系列",product_info["product_serial_name"].unique(),default=product_info["product_serial_name"].unique(),
                key = 'product_serial_name_input',placeholder = '请选择产品系列')
        if not product_serial_name_input:
            st.warning("请选择产品系列")
            return
        if product_serial_name_input:
            product_info = product_info[product_info["product_serial_name"].isin(product_serial_name_input)]
        
        # 产品集筛选
        if is_iframe == 1 and product_set_code_iframe is not None:
            product_name_input = st.multiselect('请选择产品', product_info['product_set_name'].tolist(),
                                                 default=[product_info[product_info['product_set_code'] == product_set_code_iframe]['product_set_name'].values[0]],disabled=True)
        else:
            product_name_input = st.multiselect(
                "请选择产品",product_info["product_set_name"].tolist(),placeholder = '请选择产品',key = 'product_name_input',
                help = '不选择产品，默认选择全部产品')

        if not product_name_input:
            product_name_input = product_info["product_set_name"].tolist()
        product_set_code = product_info[product_info["product_set_name"].isin(product_name_input)]["product_set_code"].tolist()
        product_set_str = "','".join(product_set_code)

        product_code = product_info[product_info["product_set_name"].isin(product_name_input)]["product_code"].tolist()
        product_code_str = ",".join(product_code)

        sale_until = dt.date.today()
        sale_from = dt.date(2000, 1, 1)
        # 创建一个具有当前日期和时间为 23:59:59 的 datetime 对象
        default_end = dt.datetime.combine(sale_until, dt.time(hour=23, minute=59, second=59))
        default_start = dt.datetime.combine(sale_until, dt.time(hour=0, minute=0, second=0))
        cols = st.columns([1,3,3,2])
        with cols[0]:
            date_type = st.radio("模式",["日期", "日期时间"],index=0,key="date_type")
        if date_type == "日期":
            with cols[1]:
                start_date = st.date_input('开始日期', min_value=sale_from, max_value=sale_until, value=sale_until,
                                     key='start_date')
            with cols[2]:
                end_date = st.date_input('截止日期', min_value=sale_from, max_value=sale_until, value=sale_until,
                                     key='end_date')
                start_datetime = dt.datetime.combine(start_date, dt.time(hour=0, minute=0, second=0))
                end_datetime = dt.datetime.combine(end_date, dt.time(hour=23, minute=59, second=59))
        else:
            with cols[1]:
                text_new("日期范围", 14)
                date_range_string = date_range_picker(
                    picker_type=PickerType.time,
                    start=default_start,
                    end=default_end,
                    key="time_range_picker",
                )

                if date_range_string is None:
                    start_datetime = default_start
                    end_datetime = default_end
                else:
                    start_datetime = date_range_string[0]
                    end_datetime = date_range_string[1]
        with cols[3]:
            name_standard = st.selectbox('姓名标准化处理', ['是', '否'], key='name_standard')
        st.divider()
        with st.spinner("数据处理中，请稍候..."):
            # 根据初审还是复审选择需要展示的表格
            df,df_daily = get_auditor_workload(product_set_code=product_set_str,start_datetime=start_datetime,end_datetime=end_datetime,name_standard=name_standard)
            df_yz,df_yz_daily = get_auditor_workload_yz(product_code=product_code_str,start_datetime=start_datetime,end_datetime=end_datetime,name_standard=name_standard)
            df_gz,df_gz_daily = get_auditor_workload_gz(product_set_code=product_set_str,start_datetime=start_datetime,end_datetime=end_datetime,name_standard=name_standard)
            df_js,df_js_daily = get_auditor_workload_v3(product_set_code=product_set_str,start_datetime=start_datetime,end_datetime=end_datetime,name_standard=name_standard,sql='SQL_AUDITOR_WORKLOAD_JS')
            df_nm,df_nm_daily = get_auditor_workload_v3(product_set_code=product_set_str,start_datetime=start_datetime,end_datetime=end_datetime,name_standard=name_standard,sql='SQL_AUDITOR_WORKLOAD_V3_1')
            df_v3_2,df_v3_2_daily = get_auditor_workload_v3(product_set_code=product_set_str,start_datetime=start_datetime,end_datetime=end_datetime,name_standard=name_standard,sql='SQL_AUDITOR_WORKLOAD_V3_2')
            df_v3_yc,df_v3_yc_daily = get_auditor_workload_v3(product_set_code=product_set_str,start_datetime=start_datetime,end_datetime=end_datetime,name_standard=name_standard,sql='SQL_AUDITOR_WORKLOAD_V3_YC')
            df_v3_jj,df_v3_jj_daily = get_auditor_workload_v3(product_set_code=product_set_str, start_datetime=start_datetime,end_datetime=end_datetime, name_standard=name_standard,sql='SQL_AUDITOR_WORKLOAD_V3_JJ')
            
            # 合并数据
            df_total = pd.concat([df,df_yz])
            df_total = pd.concat([df_total,df_gz])
            df_total = pd.concat([df_total,df_js])
            df_total = pd.concat([df_total,df_nm])
            df_total = pd.concat([df_total,df_v3_2])
            df_total = pd.concat([df_total,df_v3_yc])
            df_total = pd.concat([df_total, df_v3_jj])
            df_total = df_total.reset_index(drop=True)

            # 日度数据并
            df_daily = pd.concat([df_daily,df_yz_daily])
            df_daily = pd.concat([df_daily,df_gz_daily])
            df_daily = pd.concat([df_daily,df_js_daily])
            df_daily = pd.concat([df_daily,df_nm_daily])
            df_daily = pd.concat([df_daily,df_v3_2_daily])
            df_daily = pd.concat([df_daily,df_v3_yc_daily])
            df_daily = pd.concat([df_daily,df_v3_jj_daily])
            df_daily = df_daily.reset_index(drop=True)

            # 确保日期类型为datetime
            if isinstance(start_datetime, str):
                start_datetime = dt.datetime.strptime(start_datetime, '%Y-%m-%d %H:%M:%S')
            if isinstance(end_datetime, str):
                end_datetime = dt.datetime.strptime(end_datetime, '%Y-%m-%d %H:%M:%S')
            days = get_workdays_count(start_datetime.date(),end_datetime.date())
            text_write("审核人员工作统计")
            df_group  = df_total.groupby(['审核人员']).agg({'数量':sum_or_combine,'工作量':sum_or_combine}).reset_index()
            # 添加合计行
            df_group['指标'] = 420*days
            df_group['工作量'] = df_group['工作量'].apply(lambda x: round(x,0))
            df_group['达成率'] = df_group.apply(lambda x: round(x['工作量']/x['指标']*100,2),axis=1)
            df_group.sort_values(by=['达成率'],inplace=True,ascending=False)
            df_group['达成率'] = df_group['达成率'].apply(lambda x: f"{x}%")
            df_group.reset_index(inplace=True,drop=True)
            df_group.drop(columns=['数量'],inplace=True)

            # 日度数据处理
            df_daily_group = df_daily.groupby(['审核人员','审核日期']).agg({'数量':sum_or_combine,'工作量':sum_or_combine}).reset_index()
            df_daily_group['指标'] = 420
            df_daily_group['工作量'] = df_daily_group['工作量'].apply(lambda x: round(x,0))
            df_daily_group['达成率'] = df_daily_group.apply(lambda x: round(x['工作量']/x['指标']*100,2),axis=1)
            df_daily_group.sort_values(by=['审核人员','审核日期'],inplace=True)
            df_daily_group.reset_index(inplace=True,drop=True)
            df_daily_group.drop(columns=['数量'],inplace=True)

            row_select = st.dataframe(df_group,use_container_width=True,hide_index=True, selection_mode='multi-row',
                              on_select="rerun")
            # 处理合并表格的选择事件
            if row_select['selection']['rows']:
                select_list = [df_group.index[i] for i in row_select['selection']['rows']]
                selected_cases = df_group.loc[select_list]['审核人员'].unique().tolist()

                # 使用通用函数创建日度明细表格
                create_daily_summary_table(df_daily_group, selected_cases)

            text_write("审核人员分项目工作统计")
            
            # Process and display workload for each product
            if not df.empty:
                sub_text_write("南京宁惠保")
                text_new('总计=A*3+B*7+C*14+C2*21+C3*28+C4*35+C5*42+C6*49+C7*56+C8*63+D*5+E*1+F*1+G*2+H*3+I*6+J*12+K*18+L*24+M*30+N*36+O*42+P*48+Q*54')
                result_df = process_workload_df(df, "summary", days)
                st.dataframe(result_df, use_container_width=True, hide_index=True)


            if not df_gz.empty:
                sub_text_write("贵州惠民保")
                text_new('总计=A*3+B*10+C*16+D*5+E*8')
                result_df = process_workload_df(df_gz, "total", days)
                st.dataframe(result_df, use_container_width=True, hide_index=True)


            if not df_yz.empty:
                sub_text_write("扬子")
                text_new('总计=A*0.45+B*0.4')
                result_df = process_workload_df(df_yz, "total", days)
                st.dataframe(result_df, use_container_width=True, hide_index=True)


            if not df_js.empty:
                sub_text_write("江苏医惠保")
                text_new('总计=A*2+B*8+B2*16+B3*24+B4*32+B5*40+B6*48+B7*56+B8*64')
                result_df = process_workload_df(df_js, "summary", days)
                st.dataframe(result_df, use_container_width=True, hide_index=True)

            
            for i in df_nm['项目名称'].unique().tolist():
                sub_text_write(i)
                text_new('总计=A*4+B*8+C*8')
                df_temp = df_nm[df_nm['项目名称']==i]
                if not df_temp.empty:
                    result_df = process_workload_df(df_temp, "total", days)
                    st.dataframe(result_df, use_container_width=True, hide_index=True)

            
            for i in df_v3_2['项目名称'].unique().tolist():
                sub_text_write(i)
                text_new('总计=A*5+B*8+C*10+D*12+E*8+F*10')
                df_temp = df_v3_2[df_v3_2['项目名称']==i]
                if not df_temp.empty:
                    result_df = process_workload_df(df_temp, "total", days)
                    st.dataframe(result_df, use_container_width=True, hide_index=True)

            
            for i in df_v3_yc['项目名称'].unique().tolist():
                sub_text_write(i)
                text_new('总计=A*6+B1*13+B2*17+B3*25+B4*32+B5*42+B6*60+B7*80+B8+100+B9*120+B10*140+B11*160+B12*180+B13*200+D1*12+D2*14*D3*16+D4*20'
                         '+D5*23*D6*26+D7*34+D8*43+D9*52+D10*65+D11*80+D12*95+D13*110')
                df_temp = df_v3_yc[df_v3_yc['项目名称']==i]
                if not df_temp.empty:
                    result_df = process_workload_df(df_temp, "total", days)
                    st.dataframe(result_df, use_container_width=True, hide_index=True)

            for i in df_v3_jj['项目名称'].unique().tolist():
                sub_text_write(i)
                text_new(
                    '总计=A*6+B1*16+B2*22+B3*28+B4*34+B5*42+B6*62+B7*85+B8+105+B9*125+B10*145+B11*165+B12*185+B13*210+D1*15+D2*18*D3*21+D4*24'
                    '+D5*28*D6*34+D7*50+D8*70+D9*90+D10*110+D11*130+D12*150+D13*170')
                df_temp = df_v3_jj[df_v3_jj['项目名称']==i]
                if not df_temp.empty:
                    result_df = process_workload_df(df_temp, "total", days)
                    st.dataframe(result_df, use_container_width=True, hide_index=True)

    else:
        st.info('未找到产品信息')

    

if __name__ == "__main__":
    main()
