{% extends "public/data_dictionary/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
.cache-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.cache-card-header {
    background: linear-gradient(135deg, #0D6EFD 0%, #0056b3 100%);
    color: white;
    padding: 16px 20px;
    font-weight: 600;
}

.cache-card-body {
    padding: 20px;
}

.cache-status {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-active {
    background: #28a745;
}

.status-inactive {
    background: #dc3545;
}

.cache-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.cache-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    text-decoration: none;
    display: inline-block;
}

.cache-btn-danger {
    background: #dc3545;
    color: white;
}

.cache-btn-danger:hover {
    background: #c82333;
    color: white;
}

.cache-btn-warning {
    background: #ffc107;
    color: #212529;
}

.cache-btn-warning:hover {
    background: #e0a800;
    color: #212529;
}

.cache-btn-info {
    background: #17a2b8;
    color: white;
}

.cache-btn-info:hover {
    background: #138496;
    color: white;
}

.cache-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.cache-info-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #0D6EFD;
}

.cache-info-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.cache-info-value {
    color: #0D6EFD;
    font-family: monospace;
}

.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'data_dictionary:database_list' %}">数据字典</a></li>
            <li class="breadcrumb-item active" aria-current="page">缓存管理</li>
        </ol>
    </nav>

    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-memory text-primary"></i> 缓存管理</h2>
        <button class="btn btn-outline-primary" onclick="refreshCacheStatus()">
            <i class="fas fa-sync-alt"></i> 刷新状态
        </button>
    </div>

    <!-- 消息提示 -->
    <div id="message-container"></div>

    <!-- 缓存状态卡片 -->
    <div class="cache-card">
        <div class="cache-card-header">
            <i class="fas fa-info-circle"></i> 缓存状态
        </div>
        <div class="cache-card-body">
            <div id="cache-status-content">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
        </div>
    </div>

    <!-- 缓存操作卡片 -->
    <div class="cache-card">
        <div class="cache-card-header">
            <i class="fas fa-tools"></i> 缓存操作
        </div>
        <div class="cache-card-body">
            <div class="alert alert-info">
                <strong>注意：</strong> 清空缓存会暂时影响页面加载速度，但能确保数据的最新状态。系统会自动重建缓存。
            </div>
            
            <div class="cache-actions">
                <button class="cache-btn cache-btn-danger" onclick="clearCache('all')">
                    <i class="fas fa-trash-alt"></i> 清空所有缓存
                </button>
                <button class="cache-btn cache-btn-warning" onclick="clearCache('database')">
                    <i class="fas fa-database"></i> 清空数据库缓存
                </button>
                <button class="cache-btn cache-btn-warning" onclick="clearCache('table')">
                    <i class="fas fa-table"></i> 清空表缓存
                </button>
                <button class="cache-btn cache-btn-info" onclick="refreshCacheStatus()">
                    <i class="fas fa-sync-alt"></i> 刷新状态
                </button>
            </div>
        </div>
    </div>

    <!-- 缓存说明卡片 -->
    <div class="cache-card">
        <div class="cache-card-header">
            <i class="fas fa-question-circle"></i> 缓存机制说明
        </div>
        <div class="cache-card-body">
            <h5>智能缓存特性</h5>
            <ul>
                <li><strong>自动失效</strong>：当数据发生变更时，相关缓存会自动失效</li>
                <li><strong>版本控制</strong>：使用版本号确保缓存一致性</li>
                <li><strong>模式分组</strong>：按数据类型分组管理缓存</li>
                <li><strong>信号驱动</strong>：基于Django信号自动处理缓存失效</li>
                <li><strong>优雅降级</strong>：Redis不可用时自动切换到内存缓存</li>
                <li><strong>智能检测</strong>：自动检测Redis服务状态</li>
            </ul>
            
            <h5>缓存类型</h5>
            <div class="cache-info-grid">
                <div class="cache-info-item">
                    <div class="cache-info-label">表类型缓存</div>
                    <div class="cache-info-value">table_types_{database_id}</div>
                </div>
                <div class="cache-info-item">
                    <div class="cache-info-label">数据库统计缓存</div>
                    <div class="cache-info-value">db_stats_{database_id}</div>
                </div>
                <div class="cache-info-item">
                    <div class="cache-info-label">表字段数缓存</div>
                    <div class="cache-info-value">table_columns_{table_id}</div>
                </div>
                <div class="cache-info-item">
                    <div class="cache-info-label">表索引数缓存</div>
                    <div class="cache-info-value">table_indexes_{table_id}</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    refreshCacheStatus();
});

function refreshCacheStatus() {
    $('#cache-status-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>');
    
    $.get('{% url "data_dictionary:cache_status" %}')
        .done(function(response) {
            if (response.success) {
                displayCacheStatus(response.status);
            } else {
                showMessage('获取缓存状态失败: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showMessage('获取缓存状态失败', 'danger');
        });
}

function displayCacheStatus(status) {
    const html = `
        <div class="cache-status">
            <span class="status-indicator ${status.cache_enabled ? 'status-active' : 'status-inactive'}"></span>
            <strong>缓存状态：${status.cache_enabled ? '启用' : '禁用'}</strong>
        </div>
        <div class="cache-info-grid">
            <div class="cache-info-item">
                <div class="cache-info-label">缓存版本</div>
                <div class="cache-info-value">${status.cache_version}</div>
            </div>
            <div class="cache-info-item">
                <div class="cache-info-label">缓存后端</div>
                <div class="cache-info-value">${status.cache_backend === 'django_cache' ? 'Redis缓存' : '内存缓存'}</div>
            </div>
            <div class="cache-info-item">
                <div class="cache-info-label">Redis状态</div>
                <div class="cache-info-value">${status.redis_available ? '可用' : '不可用'}</div>
            </div>
            ${status.cache_backend === 'memory_cache' ? `
            <div class="cache-info-item">
                <div class="cache-info-label">内存缓存项</div>
                <div class="cache-info-value">${status.memory_cache_size}</div>
            </div>
            ` : ''}
            <div class="cache-info-item">
                <div class="cache-info-label">自动失效</div>
                <div class="cache-info-value">${status.features.auto_invalidation ? '启用' : '禁用'}</div>
            </div>
            <div class="cache-info-item">
                <div class="cache-info-label">版本控制</div>
                <div class="cache-info-value">${status.features.version_control ? '启用' : '禁用'}</div>
            </div>
        </div>
    `;
    $('#cache-status-content').html(html);
}

function clearCache(type) {
    if (!confirm('确定要清空缓存吗？这可能会暂时影响页面加载速度。')) {
        return;
    }
    
    const data = {
        'type': type,
        'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
    };
    
    $.post('{% url "data_dictionary:clear_cache" %}', data)
        .done(function(response) {
            if (response.success) {
                showMessage(response.message, 'success');
                refreshCacheStatus();
            } else {
                showMessage('清空缓存失败: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showMessage('清空缓存失败', 'danger');
        });
}

function showMessage(message, type) {
    const alertClass = `alert-${type}`;
    const html = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('#message-container').html(html);
    
    // 3秒后自动隐藏
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}
</script>

<!-- CSRF Token -->
{% csrf_token %}
{% endblock %}
