import datetime as dt
import warnings
import time
import pymysql
import numpy as np
import idna
import pandas as pd
import streamlit as st
from streamlit.runtime.secrets import secrets_singleton
from utils.st import query_sql
from utils.utils import sum_or_combine, replace_using_dict
from utils.auth import agent_auth_check

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)

total_connection_info = secrets_singleton["connections"]

jkx_conn = total_connection_info['claim_nhb']
yangzi_conn = total_connection_info['claim_yangzi']
jiangbei_conn = total_connection_info['claim_jiangbei']
claim_slave_conn = total_connection_info['claim']
guizhou_conn = total_connection_info['claim_guizhou']

CONNECTOR = st.connection('claim', type='sql')
def get_connection(DB):
    """
    获取数据库连接
    """
    conn = pymysql.connect(host=idna.encode(DB["host"]).decode('utf-8'), port=int(DB["port"]),
                           user=DB["username"],
                           password=DB["password"], database=DB["database"])
    return conn


@st.cache_resource(ttl=600)
def get_product_code_nhb(_conn):
    """
    获取产品信息数据(包含医疗的所有产品代码)
    :param conn: 数据库连接
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
        SUBSTRING_INDEX( CODE, '-', 1 ) product_set_code,
        GROUP_CONCAT( CODE SEPARATOR "','" ) product_code,
        'CONNECTOR_JKX' conn ,'v1' version
    FROM
        product 
    WHERE
        NAME LIKE '%宁惠保%' 
        AND ( NAME LIKE '%基础版%' OR NAME LIKE '%升级版%' OR NAME LIKE '%标准版%' ) 
    GROUP BY
        SUBSTRING_INDEX( CODE, '-', 1 )
    '''
    with get_connection(_conn) as conn:
        df_product_code = pd.read_sql(SQL_PRODUCT_CODE, conn)
        # 将product_code中的字符串用单引号包裹
    df_product_code['product_code'] = df_product_code['product_code'].apply(lambda x: "'" + x + "'")
    return df_product_code



@st.cache_resource(ttl=600)
def get_product_code_gz(_conn):
    """
    获取产品信息数据(包含医疗的所有产品代码)
    :param conn: 数据库连接
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT REPLACE
        ( SUBSTRING_INDEX( CODE, '-', 1 ), 'guizhou', 'guihuibao' ) product_set_code,
        GROUP_CONCAT( CODE SEPARATOR "','" ) product_code,
        'CONNECTOR_GZ' conn,

        'v1' version 
    FROM
        product 
    GROUP BY
        REPLACE (
            SUBSTRING_INDEX( CODE, '-', 1 ),
        'guizhou',
        'guihuibao')
    '''
    with get_connection(_conn) as conn:
        df_product_code = pd.read_sql(SQL_PRODUCT_CODE, conn)
    # 将product_code中的字符串用单引号包裹
    df_product_code['product_code'] = df_product_code['product_code'].apply(lambda x: "'" + x + "'")
    return df_product_code



@st.cache_resource(ttl=600)
def get_product_code_yhb(_conn):
    """
    获取产品信息数据(包含医疗的所有产品代码)
    :param conn: 数据库连接
    :return:
    """
    SQL_PRODUCT_CODE_YHB = '''
    SELECT
        product_serial_code product_set_code,
        GROUP_CONCAT( p.CODE SEPARATOR "','" ) product_code,
        'CONNECTOR_YHB' conn,
        'v3' version 
    FROM
        product p
        JOIN product_set ps ON p.product_set_code = ps.CODE 
    WHERE
        ps.product_serial_code IN ( 'jiangsu_yhb', 'neimenggu_hmb', 'xiantao_xhb', 'shanxi_jkb', 'hunan_amb' ) 
    GROUP BY
        ps.product_serial_code
    '''
    with get_connection(_conn) as conn:
        df_product_code = pd.read_sql(SQL_PRODUCT_CODE_YHB, conn)
    # 将product_code中的字符串用单引号包裹
    df_product_code['product_code'] = df_product_code['product_code'].apply(lambda x: "'" + x + "'")
    return df_product_code


@st.cache_resource(ttl=600)
def get_product_code_yc(_conn):
    """
    获取产品信息数据(包含医疗的所有产品代码)
    :param conn: 数据库连接
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
        product_serial_code product_set_code,
        GROUP_CONCAT( p.CODE SEPARATOR "','" ) product_code,
        'CONNECTOR_YHB' conn,
        'v1' version 
    FROM
        product p
        JOIN product_set ps ON p.product_set_code = ps.CODE 
    WHERE
        ps.product_serial_code IN ( 'yichun_hmb', 'jiujiang_hxb') 
    GROUP BY
        ps.product_serial_code
    '''
    with get_connection(_conn) as conn:
        df_product_code = pd.read_sql(SQL_PRODUCT_CODE, conn)
    # 将product_code中的字符串用单引号包裹
    df_product_code['product_code'] = df_product_code['product_code'].apply(lambda x: "'" + x + "'")
    return df_product_code


@st.cache_resource(ttl=600)
def get_product_code_yz(_conn):
    """
    获取扬子产品信息数据
    :param conn: 数据库连接
    :return:
    """
    SQL_PRODUCT_CODE_YZ = '''
            SELECT
    	product_set_code,
    	GROUP_CONCAT( product_code SEPARATOR "','" ) product_code ,
    	'CONNECTOR_YZ' conn,
    	'v1' version
    FROM
    	(
    	SELECT
    		name product_name,
    		CODE product_code ,
    		SUBSTRING_INDEX(CODE, '-', 1) product_set_code
    	FROM
    		product 
    	WHERE
    	CODE like 'yangzi%'
    	or CODE like 'boxianyuan%'
        or code like 'taizhou%'
			or code like 'qingjiang%') t 
    GROUP BY
    	product_set_code
        '''
    with get_connection(_conn) as conn:
        df_product_code_yz = pd.read_sql(SQL_PRODUCT_CODE_YZ, conn)
    df_product_code_yz['product_code'] = df_product_code_yz['product_code'].apply(lambda x: "'" + x + "'")
    return df_product_code_yz


def get_initial_auditor_time_pass_days(product_code, product_name, start_date, end_date, days=3,
                                       is_equal=True, conn=jkx_conn,
                                       sql=query_sql('SQL_WAIT_AUDIT_NUMBER')):
    """
    初审人员T+几天情况
    :param product_code: 产品code
    :param product_set_code: 产品名称
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param days: 时间间隔
    :param is_equal:是否等于，默认等于
    :param sql:查询sql语句
    :return:
    """
    status_type = "'CLAIM_APPLICATION_WAIT_AUDIT','CLAIM_APPLICATION_AUDITED','TEMPORARY_SAVED', 'REVIEW_REJECTED'"
    sql += f" and date(CONVERT_TZ(a.issue_time,'+0:00','+8:00')) >='{start_date}' and date(CONVERT_TZ(a.issue_time,'+0:00','+8:00')) <='{end_date}'"
    if is_equal:
        sql += f" and DATE_FORMAT(CONVERT_TZ(a.issue_time,'+0:00','+8:00'), '%Y-%m-%d') = date_sub('{end_date}', interval {days} day)"
    else:
        sql += f" and DATE_FORMAT(CONVERT_TZ(a.issue_time,'+0:00','+8:00'), '%Y-%m-%d') <= date_sub('{end_date}', interval {days} day)"
    sql += f" GROUP BY a.product_code;"
    with get_connection(conn) as conn:
        df_initial_auditor_time_pass_days = pd.read_sql(
            sql.format(product_code=product_code, days=days, start_date=start_date, end_date=end_date,
                       status_type=status_type), conn)
    df_initial_auditor_time_pass_days.rename(columns={'wait_num': f'超T+{days}'}, inplace=True)
    df_initial_auditor_time_pass_days = df_initial_auditor_time_pass_days[['product_code', f'超T+{days}']]
    total_num = df_initial_auditor_time_pass_days[f'超T+{days}'].sum()
    df = pd.DataFrame({'product_name': [product_name], f'超T+{days}': [total_num]})
    return df


def get_review_auditor_time_pass_days(product_code, product_name, start_date, end_date, days=3, is_equal=True,
                                      conn=jkx_conn, sql=query_sql('SQL_WAIT_AUDIT_NUMBER')):
    """
    复审人员T+3情况
    :param product_code: 产品code
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param days: 时间间隔
    :param version: sql版本，因为理赔表结构不同
    :param is_equal:是否等于，默认等于
    :param sql:查询sql语句
    :return:
    """
    status_type = "'WAIT_REVIEW'"
    sql += f" and date(CONVERT_TZ(a.issue_time,'+0:00','+8:00')) >='{start_date}' and date(CONVERT_TZ(a.issue_time,'+0:00','+8:00')) <='{end_date}'"
    if is_equal:
        sql += f" and DATE_FORMAT(CONVERT_TZ(a.issue_time,'+0:00','+8:00'), '%Y-%m-%d') = date_sub('{end_date}', interval {days} day)"
    else:
        sql += f" and DATE_FORMAT(CONVERT_TZ(a.issue_time,'+0:00','+8:00'), '%Y-%m-%d') <= date_sub('{end_date}', interval {days} day)"
    sql += f"  GROUP BY a.product_code;"
    with get_connection(conn) as conn:
        df_review_auditor_time_pass_days = pd.read_sql(
            sql.format(product_code=product_code, days=days, start_date=start_date, end_date=end_date,
                       status_type=status_type), conn)
    df_review_auditor_time_pass_days.rename(columns={'wait_num': f'超T+{days}'}, inplace=True)
    df_review_auditor_time_pass_days = df_review_auditor_time_pass_days[['product_code', f'超T+{days}']]
    total_num = df_review_auditor_time_pass_days[f'超T+{days}'].sum()
    df = pd.DataFrame({'product_name': [product_name], f'超T+{days}': [total_num]})
    return df


def get_initial_additor_num(product_code, product_name, start_date, end_date, conn=jkx_conn,
                            sql=query_sql('SQL_INITIAL_AUDITOR_NUM_V2')):
    """
    获取初审人员单量、发票量数据
    :param product_code: 产品代码
    :param product_name: 产品名称
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    if start_date and end_date:
        sql += f" and date(CONVERT_TZ( a.create_time, '+0:00', '+8:00' )) >= '{end_date}' and date(CONVERT_TZ( a.create_time, '+0:00', '+8:00' )) <= '{end_date}'"
    elif start_date:
        sql += f" and date(CONVERT_TZ( a.create_time, '+0:00', '+8:00' )) >= '{end_date}'"
    elif end_date:
        sql += f" and date(CONVERT_TZ( a.create_time, '+0:00', '+8:00' )) <= '{end_date}'"

    sql += f" GROUP BY c.product_code"
    with get_connection(conn) as conn:
        df_initial_additor_num = pd.read_sql(sql.format(product_code=product_code), conn)
    df_initial_additor_num.rename(
        columns={'auditorName': '姓名', 'numberOfClaim': '通过量', 'numberOfInvoice': '通过发票'}, inplace=True)
    df_initial_additor_num['通过量'] = df_initial_additor_num['通过量'].astype(int)
    df_initial_additor_num['通过发票'] = df_initial_additor_num['通过发票'].astype(int)

    number_of_claim = df_initial_additor_num['通过量'].sum()
    number_of_invoice = df_initial_additor_num['通过发票'].sum()
    df = pd.DataFrame({'product_name': [product_name], '通过量': [number_of_claim],
                       '通过发票': [number_of_invoice]})
    df['审核'] = '初审'
    return df


def get_canceled_number(product_code,product_serial_code, product_name, start_date, end_date, role_code, conn,
                        sql=query_sql('SQL_CANCELED_NUMBER_V4')):
    """
    获取撤案单量数据
    :param product_code:产品code
    :param start_datetime:开始时间
    :param end_datetime:结束时间
    :param conn:数据库连接，由于数据不在一个库，所以需要定义不同的链接
    :param sql:数据查询sql
    :return:
    """
    if start_date and end_date:
        sql += f" and date(CONVERT_TZ( b.create_time, '+0:00', '+8:00' )) >= '{end_date}' and date(CONVERT_TZ( b.create_time, '+0:00', '+8:00' )) <= '{end_date}'"
    elif start_date:
        sql += f" and date(CONVERT_TZ( b.create_time, '+0:00', '+8:00' )) >= '{end_date}'"
    elif end_date:
        sql += f" and date(CONVERT_TZ( b.create_time, '+0:00', '+8:00' )) <= '{end_date}'"

    if role_code == 'initial_auditor':
        sql += f" and g.code = '{role_code}'"
    elif role_code == 'reviewer':
        sql += f" and g.code = '{role_code}'"

    sql += f" GROUP BY b.operator_name"
    with get_connection(conn) as conn:
        df = pd.read_sql(sql.format(product_code=product_code,product_serial_code=product_serial_code), conn)
    df.rename(columns={'fullname': '姓名', 'operator_name': '人员编码', 'canceled_num': '撤案量'},
              inplace=True)
    if df.empty:
        df_total = pd.DataFrame({'product_name': [product_name], '撤案量': [0]})
    else:
        df_total = pd.DataFrame({'product_name': [product_name], '撤案量': df['撤案量'].sum()})
    if role_code == 'initial_auditor':
        df_total['审核'] = '初审'
    elif role_code == 'reviewer':
        df_total['审核'] = '复审'
    return df_total


def get_reject_number(product_code, product_name, start_date, end_date, role_code, conn,
                      sql=query_sql('SQL_REJECT_NUMBER_V2')):
    """
    获取驳回单量数据
    :param product_code:产品code
    :param product_name:产品名称
    :param start_date:开始时间
    :param end_date:结束时间
    :param role_code:角色代码,initial_auditor-初审人员，reviewer-复审人员
    :param conn:数据库连接，由于数据不在一个库，所以需要定义不同的链接
    :param sql:数据查询sql
    :return:
    """
    if role_code == 'initial_auditor':
        status_type = "'SYSTEM_REJECTED'"
    else:
        status_type = "'REVIEW_REJECTED'"

    if start_date and end_date:
        sql += f" and date(CONVERT_TZ( b.create_time, '+0:00', '+8:00' )) >= '{end_date}' and date(CONVERT_TZ( b.create_time, '+0:00', '+8:00' )) <= '{end_date}'"
    elif start_date:
        sql += f" and date(CONVERT_TZ( b.create_time, '+0:00', '+8:00' )) >= '{end_date}'"
    elif end_date:
        sql += f" and date(CONVERT_TZ( b.create_time, '+0:00', '+8:00' )) <= '{end_date}'"

    sql += f" GROUP BY a.product_code"
    with get_connection(conn) as conn:
        df_reject_number = pd.read_sql(sql.format(product_code=product_code, status_type=status_type), conn)
    df_reject_number.rename(columns={'reject_num': '驳回量'},
                            inplace=True)
    rejection_num = df_reject_number['驳回量'].sum()
    df = pd.DataFrame({'product_name': [product_name], '驳回量': [rejection_num]})
    if role_code == 'initial_auditor':
        df['审核'] = '初审'
    elif role_code == 'reviewer':
        df['审核'] = '复审'
    return df


def get_wait_audit_number(product_code, product_name, start_date, end_date, role_code, conn,
                          sql=query_sql('SQL_WAIT_AUDIT_NUMBER')):
    """
    获取未审核单量数据
    :param product_code:产品code
    :param product_name:产品名称
    :param start_date:开始时间
    :param end_date:结束时间
    :param role_code:角色代码,initial_auditor-初审人员，reviewer-复审人员
    :param conn:数据库连接，由于数据不在一个库，所以需要定义不同的链接
    :param sql:数据查询sql
    :return:
    """
    if start_date and end_date:
        sql += f" and date(CONVERT_TZ( a.issue_time, '+0:00', '+8:00' )) >= '{start_date}' and date(CONVERT_TZ( a.issue_time, '+0:00', '+8:00' )) <= '{end_date}'"
    elif start_date:
        sql += f" and date(CONVERT_TZ( a.issue_time, '+0:00', '+8:00' )) >= '{start_date}'"
    elif end_date:
        sql += f" and date(CONVERT_TZ( a.issue_time, '+0:00', '+8:00' )) <= '{end_date}'"
    if role_code == 'initial_auditor':
        status_type = "'CLAIM_APPLICATION_WAIT_AUDIT','CLAIM_APPLICATION_AUDITED','TEMPORARY_SAVED', 'REVIEW_REJECTED'"
    else:
        status_type = "'WAIT_REVIEW'"
    sql += f" GROUP BY a.product_code"
    with get_connection(conn) as conn:
        df = pd.read_sql(sql.format(product_code=product_code, status_type=status_type), conn)
    df.rename(columns={'fullname': '姓名', 'operator_name': '人员编码', 'wait_num': '未审核单量'},
              inplace=True)
    wait_num = df['未审核单量'].sum()
    df = pd.DataFrame({'product_name': [product_name], '未审核单量': [wait_num]})
    if role_code == 'initial_auditor':
        df['审核'] = '初审'
    elif role_code == 'reviewer':
        df['审核'] = '复审'
    return df


def get_review_additor_num(product_code, product_name, start_date, end_date, version='v1', conn=jkx_conn,
                           sql=query_sql('SQL_REVIEW_AUDITOR_NUM')):
    """
    获取复审人员单量、发票量数据
    :param product_code: 产品代码
    :param product_name: 产品名称
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    if version == 'v3':
        sql = query_sql('SQL_REVIEW_AUDITOR_NUM_V2_4')
    if start_date and end_date:
        sql += f" and date(CONVERT_TZ( a.create_time, '+0:00', '+8:00' )) >= '{end_date}' and date(CONVERT_TZ( a.create_time, '+0:00', '+8:00' )) <= '{end_date}'"
    elif start_date:
        sql += f" and date(CONVERT_TZ( a.create_time, '+0:00', '+8:00' )) >= '{end_date}'"
    elif end_date:
        sql += f" and date(CONVERT_TZ( a.create_time, '+0:00', '+8:00' )) <= '{end_date}'"

    sql += f" GROUP BY c.product_code"
    with get_connection(conn) as conn:
        df_review_additor_num = pd.read_sql(sql.format(product_code=product_code), conn)
    df_review_additor_num.rename(
        columns={'auditorName': '姓名', 'numberOfClaim': '通过量', 'numberOfInvoice': '通过发票'}, inplace=True)
    df_review_additor_num['通过量'] = df_review_additor_num['通过量'].astype(int)
    df_review_additor_num['通过发票'] = df_review_additor_num['通过发票'].astype(int)
    number_of_claim = df_review_additor_num['通过量'].sum()
    number_of_invoice = df_review_additor_num['通过发票'].sum()
    df = pd.DataFrame({'product_name': [product_name], '通过量': [number_of_claim],
                       '通过发票': [number_of_invoice]})
    df['审核'] = '复审'
    return df





def get_initial_status_change_cases(product_serial_code,end_date,sql = query_sql('SQL_INITIAL_STATUS_CHANGE_PASS_DAY_V1')):
    """
    获取转状态案件数据
    :param product_serial_code: 产品集代码
    :param end_date: 截止日期
    :return: DataFrame
    """
    df = CONNECTOR.query(sql.format(product_serial_code=product_serial_code,end_date=end_date), ttl=10)
    df['类型'] = '转状态案件'
    return df

def get_initial_new_cases(product_serial_code,end_date,sql = query_sql('SQL_INITIAL_NEW_CASE_PASS_DAY_V1')):
    """
    获取转状态案件数据
    :param product_serial_code: 产品集代码
    :param end_date: 截止日期
    :return: DataFrame
    """
    df = CONNECTOR.query(sql.format(product_serial_code=product_serial_code,end_date=end_date), ttl=10)
    df['类型'] = '新案件'
    return df


def get_review_status_change_cases(product_serial_code,end_date,sql = query_sql('SQL_REVIEW_STATUS_CHANGE_PASS_DAY_V1')):
    """
    获取转状态案件数据
    :param product_serial_code: 产品集代码
    :param end_date: 截止日期
    :return: DataFrame
    """
    df = CONNECTOR.query(sql.format(product_serial_code=product_serial_code,end_date=end_date), ttl=10)
    df['类型'] = '转状态案件'
    return df

def get_review_new_cases(product_serial_code,end_date,sql = query_sql('SQL_REVIEW_NEW_CASE_PASS_DAY_V1')):
    """
    获取转状态案件数据
    :param product_serial_code: 产品集代码
    :param end_date: 截止日期
    :return: DataFrame
    """
    df = CONNECTOR.query(sql.format(product_serial_code=product_serial_code,end_date=end_date), ttl=10)
    df['类型'] = '新案件'
    return df


def process_data(status_change_cases, new_cases, product_serial_name_list):
    """处理案件数据的通用函数
    :param status_change_cases: 转状态案件数据
    :param new_cases: 新案件数据
    :param product_serial_name_list: 产品系列名称列表
    :return: 处理后的DataFrame
    """
    # 合并数据
    df_data = pd.concat([status_change_cases, new_cases], axis=0)
    
    # 创建透视表
    df_pivot = df_data.pivot_table(
        index=['产品系列', '类型'],
        columns='超时效',
        values='数量',
        aggfunc='sum'
    ).reset_index()
    
    # 处理多级索引列名
    if isinstance(df_pivot.columns, pd.MultiIndex):
        df_pivot.columns = ['产品系列' if col[0] == '产品系列' else
                           '类型' if col[0] == '类型' else
                           col[1] for col in df_pivot.columns]
    
    # 创建完整的产品系列和类型组合模板
    type_list = ['转状态案件', '新案件']
    df_template = pd.DataFrame(
        [(series, type_) for series in product_serial_name_list for type_ in type_list],
        columns=['产品系列', '类型']
    )
    
    # 合并数据并填充缺失值
    df_result = pd.merge(df_template, df_pivot, on=['产品系列', '类型'], how='left')
    df_result.fillna(0, inplace=True)
    
    # 确保包含所有必要的列
    required_columns = ['T+0', '超T+1', '超T+2', '超T+3', '超T+4', '超T+5']
    for col in required_columns:
        if col not in df_result.columns:
            df_result[col] = 0
    
    return df_result

def get_initial_data(product_serial_code, product_serial_name_list, end_date):
    """获取初审数据
    :param product_serial_code: 产品集代码
    :param product_serial_name_list: 产品系列名称列表
    :param end_date: 截止日期
    :return: DataFrame
    """
    df_initial_status_change_cases = get_initial_status_change_cases(product_serial_code, end_date)
    df_initial_new_cases = get_initial_new_cases(product_serial_code, end_date)
    return process_data(df_initial_status_change_cases, df_initial_new_cases, product_serial_name_list)

def get_review_data(product_serial_code, product_serial_name_list, end_date):
    """获取复审数据
    :param product_serial_code: 产品集代码
    :param product_serial_name_list: 产品系列名称列表
    :param end_date: 截止日期
    :return: DataFrame
    """
    df_review_status_change_cases = get_review_status_change_cases(product_serial_code, end_date)
    df_review_new_cases = get_review_new_cases(product_serial_code, end_date)
    return process_data(df_review_status_change_cases, df_review_new_cases, product_serial_name_list)


def get_total_data(product_serial_code, product_serial_name_list, end_date):
    """获取总数据
    :param product_serial_code: 产品集code
    :param product_serial_name_list: 产品系列名称列表
    :param end_date: 截止日期
    :return: DataFrame
    """
    # 获取数据
    df_initial_data = get_initial_data(product_serial_code, product_serial_name_list, end_date)
    df_review_data = get_review_data(product_serial_code, product_serial_name_list, end_date)
    # 如果存在其他列，直接删除，说明选择了历史事件
    if '其他' in df_initial_data.columns:
        df_initial_data.drop(columns=['其他'], inplace=True)
    if '其他' in df_review_data.columns:
        df_review_data.drop(columns=['其他'], inplace=True)

    # 为每个DataFrame添加审核类型列并计算每个产品系列的总和
    df_initial_data['审核'] = '初审'
    df_review_data['审核'] = '复审'

    # 获取所有数值列
    value_columns = [col for col in df_initial_data.columns if col not in ['产品系列', '类型', '审核']]

    # 对每个数据集按产品系列分组并计算总和
    df_initial_sum = df_initial_data.groupby('产品系列')[value_columns].sum().reset_index()
    df_initial_sum['审核'] = '初审'
    
    df_review_sum = df_review_data.groupby('产品系列')[value_columns].sum().reset_index()
    df_review_sum['审核'] = '复审'

    # 合并初审和复审数据
    df_result = pd.concat([df_initial_sum, df_review_sum])

    # 确保包含所有必要的列
    required_columns = ['T+0', '超T+1', '超T+2', '超T+3', '超T+4', '超T+5']
    for col in required_columns:
        if col not in df_result.columns:
            df_result[col] = 0
            if col not in value_columns:
                value_columns.append(col)


    # 确保包含所有产品系列
    all_combinations = pd.DataFrame([(series, audit_type) 
                                   for series in product_serial_name_list 
                                   for audit_type in ['初审', '复审']],
                                  columns=['产品系列', '审核'])

    df_result = pd.merge(all_combinations, df_result, on=['产品系列', '审核'], how='left')
    df_result.fillna(0, inplace=True)

    # 列排序
    df_result = df_result[['产品系列', '审核'] + value_columns]
    df_result.rename(columns={'产品系列': 'product_name','T+0':'超T+0'}, inplace=True)
    return df_result

def get_info(df, start_date, end_date, days):
    """
    获取完整的数据报表
    :param df: 涵盖产品编码、sql版本、数据库连接信息的dataframe
    :param product_set_code: 产品集编码
    :param start_date: 开始日期
    :param end_date: 截止日期
    :param days:超时天数，这边用来进行遍历，例如5，则从0到5天
    :return:
    """
    map_dict = {'CONNECTOR_YHB': claim_slave_conn, 'CONNECTOR_YZ': yangzi_conn, 'CONNECTOR_JKX': jkx_conn,'CONNECTOR_GZ': guizhou_conn}
    # 创建一个字典来保存不同产品代码的处理结果
    df_initial = pd.DataFrame(columns=['product_name'])
    df_initial['审核'] = '初审'
    df_review = pd.DataFrame(columns=['product_name'])
    df_review['审核'] = '复审'
    df_wait = pd.DataFrame(columns=['product_name'])
    df_audit = pd.DataFrame(columns=['product_name'])
    df_reject = pd.DataFrame(columns=['product_name'])
    df_canceled = pd.DataFrame(columns=['product_name'])
    df_temp = pd.DataFrame(columns=['product_name'])
    for row in range(len(df)):
        product_code = df.loc[row, 'product_code']
        product_set_code = df.loc[row, 'product_set_code']
        version = df.loc[row, 'version']
        conn = map_dict[df.loc[row, 'conn']]
        product_name = df.loc[row, 'product_name']
        product_name_list = [product_name]
        # 初审人员T+3情况
        if product_set_code in ['neimenggu_hmb','xiantao_xhb','shanxi_jkb','hunan_amb']:
            df_temp_number = get_total_data(product_set_code, product_name_list, end_date)
        else:
            for i in reversed(range(days + 1)):
                if i == days:
                    is_equal = False
                else:
                    is_equal = True
                df_initial_auditor_time_pass_days = get_initial_auditor_time_pass_days(product_code, product_name,
                                                                                    start_date, end_date, i,
                                                                                    is_equal, conn=conn)
                df_initial = pd.concat([df_initial, df_initial_auditor_time_pass_days], axis=0)
                df_initial['审核'].fillna('初审', inplace=True)
                df_initial.fillna(0, inplace=True)
                df_initial = df_initial.groupby(['product_name', '审核']).sum().reset_index()
                df_review_auditor_time_pass_days = get_review_auditor_time_pass_days(product_code, product_name, start_date,
                                                                                    end_date, i, is_equal, conn=conn)
                df_review = pd.concat([df_review, df_review_auditor_time_pass_days], axis=0)
                df_review['审核'].fillna('复审', inplace=True)
                df_review.fillna(0, inplace=True)
                df_review = df_review.groupby(['product_name', '审核']).sum().reset_index()

        for i in ['initial_auditor', 'reviewer']:
            if i == 'initial_auditor':
                df_wait_audit_number = get_wait_audit_number(product_code, product_name, start_date, end_date, i, conn)
                df_reject_number = get_reject_number(product_code, product_name, start_date, end_date, i, conn)
                df_audit_num = get_initial_additor_num(product_code, product_name, start_date, end_date, conn)
                if product_set_code == 'jiangsu_yhb':
                    df_canceled_number = get_canceled_number(product_code, product_set_code, product_name, start_date,
                                                             end_date, i, conn)
                else:
                    df_canceled_number = pd.DataFrame({'product_name': [product_name], '审核': ['初审'], '撤案量': [0]})
            else:
                df_wait_audit_number = get_wait_audit_number(product_code, product_name, start_date, end_date, i, conn)
                df_reject_number = get_reject_number(product_code, product_name, start_date, end_date, i, conn)
                df_audit_num = get_review_additor_num(product_code, product_name, start_date, end_date, version, conn)
                if product_set_code == 'jiangsu_yhb':
                    df_canceled_number = get_canceled_number(product_code, product_set_code, product_name, start_date,
                                                             end_date, i, conn)
                else:
                    df_canceled_number = pd.DataFrame({'product_name': [product_name], '审核': ['复审'], '撤案量': [0]})
            df_wait = pd.concat([df_wait, df_wait_audit_number], axis=0)
            df_audit = pd.concat([df_audit, df_audit_num], axis=0)
            df_reject = pd.concat([df_reject, df_reject_number])
            df_canceled = pd.concat([df_canceled, df_canceled_number]) 
            df_temp = pd.concat([df_temp, df_temp_number])   
            df_temp =  df_temp.drop_duplicates() 
    df = pd.concat([df_initial, df_review], axis=0)
    df = pd.concat([df, df_temp], axis=0)
    df = pd.merge(df, df_wait, on=['product_name', '审核'], how='outer')
    df = pd.merge(df, df_audit, on=['product_name', '审核'], how='outer')
    df = pd.merge(df, df_reject, on=['product_name', '审核'], how='outer')
    df = pd.merge(df, df_canceled, on=['product_name', '审核'], how='outer')
    df['撤案量'] = df.apply(lambda x: x['撤案量'] if x['product_name'] == '江苏医惠保' else 0, axis=1)
    df['处理量'] = df['通过量'] + df['驳回量'] + df['撤案量']
    # 剔除医惠保的复审数据
    df = df[(df['审核'] != '复审') | (df['product_name'] != '江苏医惠保')]
    df.sort_values(by=['product_name', '审核'], inplace=True)
    df.rename(columns={'product_name': '项目', '超T+0': 'T+0'}, inplace=True)
    number_sum = df.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
    df = pd.concat([df, number_sum], axis=0).reset_index(drop=True)
    return df


def main():
    is_iframe, user_permission, disable, product_serial_code_iframe, product_set_code_iframe = agent_auth_check()
    st.subheader("审核时效报表")
    product_info_nhb = get_product_code_nhb(jkx_conn)
    product_info_yhb = get_product_code_yhb(claim_slave_conn)
    product_info_yz = get_product_code_yz(yangzi_conn)
    product_info_yc = get_product_code_yc(claim_slave_conn)
    product_info_gz = get_product_code_gz(guizhou_conn)
    product_info = pd.concat([product_info_yhb, product_info_nhb, product_info_yz, product_info_yc,product_info_gz],
                             ignore_index=True)
    product_info['product_name'] = product_info['product_set_code'].apply(lambda x:
                                                                          replace_using_dict(x, {
                                                                              'jiangsu_yhb': '江苏医惠保',
                                                                              'boxianyuan': '玻纤院',
                                                                              'yangzi': '扬子石化',
                                                                              'ninghuibao': '南京宁惠保',
                                                                              'yichun_hmb': '宜春惠民保',
                                                                              'jiujiang_hxb': '九江惠浔保',
                                                                              'neimenggu_hmb': '内蒙古惠蒙保',
                                                                              'xiantao_xhb': '仙桃仙惠保',
                                                                              'shanxi_jkb': '山西晋康保',
                                                                              'hunan_amb': '湖南爱民保',
                                                                              'guihuibao': '贵惠保',
                                                                              'taizhou':'泰州石化',
                                                                              'qingjiang':'淮安清江石化'}))
    if product_set_code_iframe in product_info['product_set_code'].values or product_set_code_iframe is None:
        if is_iframe == 1 and product_set_code_iframe is not None:
            iframe_product_serial_name = \
                product_info[product_info['product_set_code'] == product_set_code_iframe][
                    'product_name'].values[0]
            product_name_input = st.multiselect('请选择产品名称', product_info['product_name'].tolist(),
                                                 default=[iframe_product_serial_name], disabled=True)
        else:
            product_name_input = st.multiselect('请选择产品名称', product_info['product_name'].tolist()[::-1],
                                                default=product_info['product_name'].tolist()[::-1])
        if product_name_input:
            product_info_input = product_info[product_info['product_name'].isin(product_name_input)]
            product_info_input = product_info_input.reset_index(drop=True)
        else:
            product_info_input = product_info.copy()

        # st.write(product_code_str)

        cols = st.columns([0.3, 0.3])
        sale_from = dt.date(2000, 1, 1)
        sale_until = dt.date.today()

        with cols[0]:
            start_date = st.date_input('开始日期', min_value=sale_from, max_value=sale_until,
                                       value=dt.date(2024, 1, 1))

        with cols[1]:
            end_date = st.date_input('结束日期', min_value=sale_from, max_value=sale_until, value=sale_until)
        if start_date is None:
            start_date = dt.date(2024, 1, 1)
        if end_date is None:
            end_date = sale_until
        with st.spinner('正在查询数据,耗时较长,请稍候...'):
            df_info = get_info(product_info_input, start_date, end_date, 5)
            st.divider()
            st.dataframe(df_info, use_container_width=True, hide_index=True)

    else:
        st.info('未找到产品信息')


if __name__ == '__main__':
    main()
