"""
智能字段名称翻译服务
使用pydantic-ai框架和SiliconFlow API为数据库字段提供智能中文名称翻译
"""

import logging
import requests
import json
import time
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from pydantic import BaseModel, Field
from django.conf import settings

logger = logging.getLogger(__name__)


class FieldInfo(BaseModel):
    """字段信息模型"""
    field_name: str = Field(description="字段名称")
    data_type: str = Field(description="数据类型")
    table_name: str = Field(description="表名")
    database_name: str = Field(description="数据库名")
    is_primary_key: bool = Field(default=False, description="是否主键")
    is_unique: bool = Field(default=False, description="是否唯一")
    is_nullable: bool = Field(default=True, description="是否可空")
    default_value: Optional[str] = Field(default=None, description="默认值")


class TranslationResult(BaseModel):
    """翻译结果模型"""
    field_name: str = Field(description="字段名称")
    chinese_name: str = Field(description="中文名称")
    confidence: float = Field(default=0.8, description="置信度")
    success: bool = Field(default=True, description="是否成功")
    error_message: Optional[str] = Field(default=None, description="错误信息")


class BatchTranslationResult(BaseModel):
    """批量翻译结果模型"""
    results: List[TranslationResult] = Field(description="翻译结果列表")
    total_count: int = Field(description="总数量")
    success_count: int = Field(description="成功数量")
    failed_count: int = Field(description="失败数量")


class FieldTranslationService:
    """智能字段翻译服务"""
    
    def __init__(self):
        """初始化翻译服务"""
        self.config = getattr(settings, 'AI_TRANSLATION_CONFIG', {})
        self.api_url = self.config.get('api_url')
        self.api_key = self.config.get('api_key')
        self.model = self.config.get('model', 'Qwen/Qwen3-8B')
        self.max_tokens = self.config.get('max_tokens', 1024)
        self.temperature = self.config.get('temperature', 0.3)
        self.timeout = self.config.get('timeout', 30)
        self.enabled = self.config.get('enabled', True)
        
        if not self.enabled:
            logger.warning("AI翻译功能已禁用")
        elif not self.api_key or not self.api_url:
            logger.error("AI翻译配置不完整，请检查API密钥和URL")
            self.enabled = False

    def _get_translation_prompt(self, field_info: FieldInfo) -> str:
        """生成翻译提示词"""
        # 根据字段名提供更具体的上下文提示
        context_hints = {
            'id': '通常表示唯一标识符',
            'user_id': '用户标识符',
            'create_time': '创建时间',
            'update_time': '更新时间',
            'first_channel': '第一渠道或首要渠道',
            'second_channel': '第二渠道或次要渠道',
            'third_channel': '第三渠道',
            'channel': '渠道',
            'name': '名称',
            'title': '标题',
            'content': '内容',
            'status': '状态',
            'type': '类型',
            'code': '编码',
            'phone': '电话',
            'email': '邮箱',
            'address': '地址',
            'amount': '金额',
            'price': '价格',
            'count': '数量',
            'description': '描述',
            'remark': '备注',
        }

        field_name_lower = field_info.field_name.lower()
        context_hint = ""
        for key, hint in context_hints.items():
            if key in field_name_lower:
                context_hint = f"\n- 上下文提示：{hint}"
                break

        prompt = f"""你是一个数据库字段名称翻译专家。请根据以下信息为数据库字段提供准确的中文名称。

字段信息：
- 字段名：{field_info.field_name}
- 数据类型：{field_info.data_type}
- 表名：{field_info.table_name}
- 数据库：{field_info.database_name}
- 是否主键：{'是' if field_info.is_primary_key else '否'}
- 是否唯一：{'是' if field_info.is_unique else '否'}
- 是否可空：{'是' if field_info.is_nullable else '否'}
- 默认值：{field_info.default_value or '无'}{context_hint}

翻译要求：
1. 提供简洁、准确的中文名称
2. 符合数据库字段命名规范
3. 考虑业务上下文和字段用途
4. 长度控制在2-10个汉字
5. 避免使用过于技术性的术语
6. 不要翻译成"文本信息"、"文本"、"信息"等过于宽泛的词汇
7. 要根据字段名的具体含义进行翻译

请直接返回中文名称，不需要其他解释。"""
        return prompt

    def _call_ai_api(self, prompt: str) -> Optional[str]:
        """调用AI API"""
        if not self.enabled:
            return None
            
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'model': self.model,
                'messages': [
                    {'role': 'system', 'content': '你是一个专业的数据库字段名称翻译专家。'},
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': self.max_tokens,
                'temperature': self.temperature,
                'stream': False
            }
            
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content'].strip()
                    return content
                else:
                    logger.error(f"AI API返回格式异常: {result}")
                    return None
            else:
                logger.error(f"AI API调用失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error("AI API调用超时")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"AI API调用异常: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"AI翻译过程中发生未知错误: {str(e)}")
            return None

    def translate_field(self, field_info: FieldInfo) -> TranslationResult:
        """翻译单个字段"""
        if not self.enabled:
            return TranslationResult(
                field_name=field_info.field_name,
                chinese_name="",
                success=False,
                error_message="AI翻译功能未启用"
            )
        
        try:
            # 首先尝试从数据库缓存中查找相同的翻译
            cached_result = self._get_cached_translation(field_info.field_name)
            if cached_result:
                return TranslationResult(
                    field_name=field_info.field_name,
                    chinese_name=cached_result,
                    confidence=0.95,  # 缓存结果给高置信度
                    success=True
                )

            prompt = self._get_translation_prompt(field_info)
            chinese_name = self._call_ai_api(prompt)
            
            if chinese_name:
                # 简单的结果验证和清理
                chinese_name = chinese_name.strip().replace('"', '').replace("'", '')
                
                # 验证长度
                if len(chinese_name) > 20:
                    chinese_name = chinese_name[:20]
                
                return TranslationResult(
                    field_name=field_info.field_name,
                    chinese_name=chinese_name,
                    success=True
                )
            else:
                return TranslationResult(
                    field_name=field_info.field_name,
                    chinese_name="",
                    success=False,
                    error_message="AI API调用失败"
                )
                
        except Exception as e:
            logger.error(f"翻译字段 {field_info.field_name} 时发生错误: {str(e)}")
            return TranslationResult(
                field_name=field_info.field_name,
                chinese_name="",
                success=False,
                error_message=str(e)
            )

    def translate_fields_batch(self, field_infos: List[FieldInfo]) -> BatchTranslationResult:
        """批量翻译字段（逐个翻译，带频率控制）"""
        results = []
        success_count = 0
        failed_count = 0

        # API调用频率控制：RPM 1000, TPM 50000
        # 为了安全起见，我们限制为每分钟800次请求，每次请求间隔0.075秒
        request_delay = 0.075  # 75毫秒间隔，约每分钟800次请求

        for i, field_info in enumerate(field_infos):
            # 在每次API调用前添加延迟（除了第一次）
            if i > 0:
                time.sleep(request_delay)

            result = self.translate_field(field_info)
            results.append(result)

            if result.success:
                success_count += 1
            else:
                failed_count += 1

            # 记录进度
            if (i + 1) % 10 == 0:
                logger.info(f"批量翻译进度: {i + 1}/{len(field_infos)}, 成功: {success_count}, 失败: {failed_count}")

        return BatchTranslationResult(
            results=results,
            total_count=len(field_infos),
            success_count=success_count,
            failed_count=failed_count
        )

    def translate_fields_batch_optimized(self, field_infos: List[FieldInfo]) -> BatchTranslationResult:
        """批量翻译字段（真正的批量优化版本）"""
        if not self.enabled:
            results = [
                TranslationResult(
                    field_name=field_info.field_name,
                    chinese_name="",
                    success=False,
                    error_message="AI翻译功能未启用"
                ) for field_info in field_infos
            ]
            return BatchTranslationResult(
                results=results,
                total_count=len(field_infos),
                success_count=0,
                failed_count=len(field_infos)
            )

        try:
            logger.info(f"开始批量翻译 {len(field_infos)} 个字段")

            # 第一步：批量查询缓存翻译
            cached_translations = self._get_batch_cached_translations([f.field_name for f in field_infos])
            logger.info(f"从缓存中找到 {len(cached_translations)} 个翻译")

            # 第二步：分离缓存和需要AI翻译的字段
            results = []
            fields_need_ai = []

            for field_info in field_infos:
                if field_info.field_name in cached_translations:
                    # 使用缓存翻译
                    results.append(TranslationResult(
                        field_name=field_info.field_name,
                        chinese_name=cached_translations[field_info.field_name],
                        confidence=0.95,  # 缓存结果给高置信度
                        success=True
                    ))
                else:
                    # 需要AI翻译
                    fields_need_ai.append(field_info)

            # 第三步：对需要AI翻译的字段进行批量翻译（考虑TPM限制）
            if fields_need_ai:
                logger.info(f"需要AI翻译 {len(fields_need_ai)} 个字段")

                # 考虑TPM限制（50,000 tokens/min），将大批量分割成小批次
                # 估算每个字段约100-200 tokens，为安全起见，每批次最多100个字段
                batch_size = 100
                ai_results_all = []

                for i in range(0, len(fields_need_ai), batch_size):
                    batch_fields = fields_need_ai[i:i + batch_size]
                    logger.info(f"处理批次 {i//batch_size + 1}/{(len(fields_need_ai) + batch_size - 1)//batch_size}, 字段数: {len(batch_fields)}")

                    # 批次间添加延迟，避免触发RPM限制
                    if i > 0:
                        time.sleep(1.0)  # 批次间延迟1秒

                    batch_prompt = self._get_batch_translation_prompt(batch_fields)
                    response = self._call_ai_api(batch_prompt)

                    if response:
                        # 解析AI翻译结果
                        ai_results = self._parse_batch_translation_response(response, batch_fields)
                        ai_results_all.extend(ai_results.results)
                    else:
                        # AI翻译失败，添加失败结果
                        for field_info in batch_fields:
                            ai_results_all.append(TranslationResult(
                                field_name=field_info.field_name,
                                chinese_name="",
                                success=False,
                                error_message="AI翻译服务调用失败"
                            ))

                results.extend(ai_results_all)

            # 统计结果
            success_count = sum(1 for r in results if r.success)
            failed_count = len(results) - success_count

            logger.info(f"批量翻译完成：成功 {success_count}，失败 {failed_count}")

            return BatchTranslationResult(
                results=results,
                total_count=len(field_infos),
                success_count=success_count,
                failed_count=failed_count
            )

        except Exception as e:
            logger.error(f"批量翻译过程中发生错误: {str(e)}")
            # 出错时回退到逐个翻译
            return self.translate_fields_batch(field_infos)

    def _get_batch_translation_prompt(self, field_infos: List[FieldInfo]) -> str:
        """构建批量翻译的prompt"""
        fields_text = []
        for i, field_info in enumerate(field_infos, 1):
            field_text = f"{i}. 字段名: {field_info.field_name}"
            if field_info.data_type:
                field_text += f", 数据类型: {field_info.data_type}"
            if field_info.table_name:
                field_text += f", 表名: {field_info.table_name}"
            if field_info.database_name:
                field_text += f", 数据库: {field_info.database_name}"
            fields_text.append(field_text)

        fields_list = "\n".join(fields_text)

        return f"""请为以下数据库字段提供准确的中文名称翻译。请根据字段名称、数据类型、表名和数据库名称的上下文信息，提供最合适的中文翻译。

字段列表：
{fields_list}

请按照以下JSON格式返回翻译结果，每个字段一行：
{{
  "translations": [
    {{"field_name": "字段名1", "chinese_name": "中文名称1", "confidence": 0.9}},
    {{"field_name": "字段名2", "chinese_name": "中文名称2", "confidence": 0.8}},
    ...
  ]
}}

要求：
1. 中文名称要简洁明了，不超过20个字符
2. 根据字段的业务含义提供翻译，而不是直接的字面翻译
3. confidence值范围0.0-1.0，表示翻译的置信度
4. 必须严格按照JSON格式返回，不要包含其他文字"""

    def _parse_batch_translation_response(self, response: str, field_infos: List[FieldInfo]) -> BatchTranslationResult:
        """解析批量翻译的响应"""
        results = []
        success_count = 0
        failed_count = 0

        try:
            # 尝试解析JSON响应
            response_data = json.loads(response.strip())
            translations = response_data.get('translations', [])

            # 创建字段名到翻译结果的映射
            translation_map = {t.get('field_name'): t for t in translations}

            for field_info in field_infos:
                translation = translation_map.get(field_info.field_name)
                if translation and translation.get('chinese_name'):
                    chinese_name = translation['chinese_name'].strip().replace('"', '').replace("'", '')
                    confidence = float(translation.get('confidence', 0.8))

                    # 验证长度
                    if len(chinese_name) > 20:
                        chinese_name = chinese_name[:20]

                    results.append(TranslationResult(
                        field_name=field_info.field_name,
                        chinese_name=chinese_name,
                        confidence=confidence,
                        success=True
                    ))
                    success_count += 1
                else:
                    results.append(TranslationResult(
                        field_name=field_info.field_name,
                        chinese_name="",
                        success=False,
                        error_message="未找到翻译结果"
                    ))
                    failed_count += 1

        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error(f"解析批量翻译响应失败: {str(e)}")
            # 解析失败时，为所有字段返回失败结果
            for field_info in field_infos:
                results.append(TranslationResult(
                    field_name=field_info.field_name,
                    chinese_name="",
                    success=False,
                    error_message="响应解析失败"
                ))
                failed_count += 1

        return BatchTranslationResult(
            results=results,
            total_count=len(field_infos),
            success_count=success_count,
            failed_count=failed_count
        )

    def _is_translation_quality_good(self, field_name: str, translation: str) -> bool:
        """检查翻译质量是否合理"""
        if not translation or not translation.strip():
            return False

        translation = translation.strip()

        # 检查明显不合理的翻译
        unreasonable_translations = {
            'first_channel': ['文本信息', '文本', '信息'],
            'second_channel': ['文本信息', '文本', '信息'],
            'third_channel': ['文本信息', '文本', '信息'],
            'channel': ['文本信息', '文本', '信息'],
            'id': ['文本信息', '文本', '信息'],
            'user_id': ['文本信息', '文本', '信息'],
            'create_time': ['文本信息', '文本', '信息'],
            'update_time': ['文本信息', '文本', '信息'],
            'data_type': ['文本信息', '文本', '信息'],
            'data_id': ['文本信息', '文本', '信息'],
            'old_info': ['文本信息', '文本', '信息'],
            'new_info': ['文本信息', '文本', '信息'],
        }

        # 如果字段名在不合理翻译列表中，检查翻译是否合理
        if field_name.lower() in unreasonable_translations:
            if translation in unreasonable_translations[field_name.lower()]:
                logger.warning(f"字段 {field_name} 的翻译 '{translation}' 被判定为不合理")
                return False

        # 检查翻译长度是否合理（太短或太长都可能有问题）
        if len(translation) < 2 or len(translation) > 15:
            logger.warning(f"字段 {field_name} 的翻译 '{translation}' 长度不合理")
            return False

        # 检查是否包含明显的错误标识
        error_indicators = ['错误', 'error', 'null', 'undefined', '未知']
        if any(indicator in translation.lower() for indicator in error_indicators):
            logger.warning(f"字段 {field_name} 的翻译 '{translation}' 包含错误标识")
            return False

        return True

    def _get_cached_translation(self, field_name: str) -> Optional[str]:
        """从数据库中查找相同字段名的已有翻译，只有唯一且质量好的翻译时才返回"""
        try:
            from public.models import PublicColumnInfo

            # 查找相同字段名且有中文注释的记录
            cached_columns = PublicColumnInfo.objects.filter(
                name=field_name,
                comment__isnull=False
            ).exclude(comment='').values_list('comment', flat=True).distinct()

            # 转换为列表并过滤空字符串
            unique_comments = [comment.strip() for comment in cached_columns if comment.strip()]

            # 只有当存在唯一的翻译时才进一步检查质量
            if len(unique_comments) == 1:
                translation = unique_comments[0]
                if self._is_translation_quality_good(field_name, translation):
                    logger.info(f"找到字段 {field_name} 的唯一高质量缓存翻译: {translation}")
                    return translation
                else:
                    logger.info(f"字段 {field_name} 的缓存翻译 '{translation}' 质量不佳，使用AI重新翻译")
                    return None
            elif len(unique_comments) > 1:
                logger.info(f"字段 {field_name} 存在多个不同的翻译 ({len(unique_comments)} 个)，使用AI重新翻译")
                return None
            else:
                logger.info(f"字段 {field_name} 没有找到缓存翻译")
                return None

        except Exception as e:
            logger.error(f"查询缓存翻译时发生错误: {str(e)}")
            return None

    def _get_batch_cached_translations(self, field_names: List[str]) -> Dict[str, str]:
        """批量查询字段的缓存翻译，只返回有唯一且质量好的翻译的字段"""
        try:
            from public.models import PublicColumnInfo
            from django.db.models import Count

            # 批量查询所有字段名的翻译
            cached_data = PublicColumnInfo.objects.filter(
                name__in=field_names,
                comment__isnull=False
            ).exclude(comment='').values('name', 'comment')

            # 按字段名分组统计
            field_translations = {}
            for item in cached_data:
                field_name = item['name']
                comment = item['comment'].strip()
                if comment:  # 过滤空字符串
                    if field_name not in field_translations:
                        field_translations[field_name] = set()
                    field_translations[field_name].add(comment)

            # 只返回有唯一且质量好的翻译的字段
            unique_translations = {}
            for field_name, comments in field_translations.items():
                if len(comments) == 1:
                    translation = list(comments)[0]
                    if self._is_translation_quality_good(field_name, translation):
                        unique_translations[field_name] = translation
                        logger.info(f"字段 {field_name} 找到唯一高质量缓存翻译: {translation}")
                    else:
                        logger.info(f"字段 {field_name} 的缓存翻译 '{translation}' 质量不佳，跳过缓存")
                else:
                    logger.info(f"字段 {field_name} 存在多个不同的翻译 ({len(comments)} 个)，跳过缓存")

            return unique_translations

        except Exception as e:
            logger.error(f"批量查询缓存翻译时发生错误: {str(e)}")
            return {}

    def is_enabled(self) -> bool:
        """检查翻译服务是否可用"""
        return self.enabled


# 全局翻译服务实例
translation_service = FieldTranslationService()


def get_translation_service() -> FieldTranslationService:
    """获取翻译服务实例"""
    return translation_service


def translate_field_name(field_name: str, data_type: str, table_name: str, 
                        database_name: str, **kwargs) -> Optional[str]:
    """便捷函数：翻译单个字段名称"""
    field_info = FieldInfo(
        field_name=field_name,
        data_type=data_type,
        table_name=table_name,
        database_name=database_name,
        **kwargs
    )
    
    result = translation_service.translate_field(field_info)
    return result.chinese_name if result.success else None
