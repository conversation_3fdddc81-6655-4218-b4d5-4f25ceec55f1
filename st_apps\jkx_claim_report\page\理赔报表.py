import datetime
import warnings
from pathlib import Path
import jwt
import numpy as np
import pandas as pd
import streamlit as st
from urllib.parse import quote
import streamlit_antd_components as sac
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from pyecharts import options as opts
from pyecharts.charts import Pie
from streamlit_echarts import st_pyecharts
from pandasql import sqldf
from st_aggrid import JsCode, AgGrid, GridOptionsBuilder, GridUpdateMode
from utils.auth import auth_from_session, get_agent_auth, JWT_SECRET, agent_auth_check
from utils.utils import send_feishu_message
from utils.st import query_sql, text_write, empty_line
from utils.date import get_shifted_dates

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)

CONNECTOR = st.connection('claim', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品代码、金额等信息
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
        REPLACE ( ps.NAME, pss.NAME, '' ) product_short_name,
        ps.NAME product_set_name,
        ps.CODE product_set_code,
        GROUP_CONCAT( p.CODE SEPARATOR ',' ) product_code,
        valid_from ,
        pss.code product_serial_code
    FROM
        product p
        JOIN product_set ps ON p.product_set_code = ps.
        CODE JOIN product_serial pss ON ps.product_serial_code = pss.CODE 
    where pss.code not in ('binzhou_yhb','rizhao_nxb','dezhou_hmb')
    GROUP BY
        ps.code 
    ORDER BY
        pss.CODE,
        ps.CODE desc,
        valid_from DESC
        '''

    df_product_code = CONNECTOR.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=10)
    df_product_code['product_code_list'] = df_product_code['product_code'].apply(lambda x: x.split(','))
    # 将product_code中的字符串用单引号包裹
    df_product_code['product_code'] = df_product_code['product_code_list'].apply(
        lambda x: ','.join([f"'{i}'" for i in x]))
    df_product_code.drop(columns=['product_code_list'], inplace=True)
    data_product = CONNECTOR_DW.query("select product_name,start_date 理赔开始日期,amount 销售金额 from other_product",
                                      show_spinner='查询中...', ttl=0)

    df_product_code = sqldf(
        "select a.*,b.销售金额 amount from df_product_code a left join  data_product b on a.product_set_name = b.product_name")
    df_product_code['amount'] = df_product_code['amount'].fillna(0)  # 金额填充0，便于下一期开始，还未填写的情况
    # 根据 product_serial_code 分组，并对每组内的 product_code, amount, product_set_name 进行 shift(-1) 操作
    df_product_code['product_code_shift'] = df_product_code.groupby('product_serial_code')['product_code'].shift(-1)
    df_product_code['amount_shift'] = df_product_code.groupby('product_serial_code')['amount'].shift(-1)
    df_product_code['product_name_shift'] = df_product_code.groupby('product_serial_code')['product_set_name'].shift(-1)
    df_product_code.fillna(value=np.nan, inplace=True)
    return df_product_code


def get_total_apply_count(product_code, start_date=None, end_date=None, age_start=None, age_end=None,
                          sql=query_sql('SQL_TOTAL_APPLY_COUNT_DETAIL')):
    """
    获取总申请量
    :param product_code: 产品code
    :param start_date: 数据开始日期
    :param end_date: 数据结束日期
    :param age_start: 年龄起始
    :param age_end: 年龄结束
    :param sql: 查询sql
    :return:
    """
    if start_date and end_date:
        sql += f" where issue_time >= '{start_date}' and issue_time <= '{end_date}'"
    elif start_date:
        sql += f" where issue_time >= '{start_date}'"
    elif end_date:
        sql += f" where issue_time <= '{end_date}'"

    if age_start or age_end:
        if start_date or end_date:
            sql += " AND"
        else:
            sql += " WHERE"

    if age_start and age_end:
        sql += f" age >= {age_start} AND age <= {age_end}"
    elif age_start:
        sql += f" age >= {age_start}"
    elif age_end:
        sql += f" age <= {age_end}"
    if end_date is None:
        end_date = datetime.datetime.now().strftime('%Y-%m-%d')
    df_total_apply_count = CONNECTOR.query(sql.format(product_code=product_code, end_date=end_date),
                                           show_spinner='查询中...', ttl=10)
    df_total_count = pd.DataFrame({'客户理赔申请案件次数': [df_total_apply_count['id'].count()]})
    return df_total_count


def get_total_apply_person(product_code, start_date=None, end_date=None, age_start=None, age_end=None,
                           sql=query_sql('SQL_TOTAL_APPLY_PERSON_COUNT_DETAIL')):
    """
    获取总申请人数
    :param product_code: 产品code
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param age_start: 年龄起始
    :param age_end: 年龄结束
    :param sql: 查询sql
    :return:
    """

    if start_date and end_date:
        sql += f" where issue_time >= '{start_date}' and issue_time <= '{end_date}'"
    elif start_date:
        sql += f" where issue_time >= '{start_date}'"
    elif end_date:
        sql += f" where issue_time <= '{end_date}'"

    if age_start or age_end:
        if start_date or end_date:
            sql += " AND"
        else:
            sql += " WHERE"

    if age_start and age_end:
        sql += f" age >= {age_start} AND age <= {age_end}"
    elif age_start:
        sql += f" age >= {age_start}"
    elif age_end:
        sql += f" age <= {age_end}"
    if end_date is None:
        end_date = datetime.datetime.now().strftime('%Y-%m-%d')
    df_total_apply_person_count = CONNECTOR.query(sql.format(product_code=product_code, end_date=end_date),
                                                  show_spinner='查询中...', ttl=10)
    df_total_person_count = pd.DataFrame({'客户理赔申请案件人数': [df_total_apply_person_count['id'].nunique()]})
    return df_total_person_count


def get_close_count(product_code, start_date=None, end_date=None, age_start=None, age_end=None,
                    sql=query_sql('SQL_CLOSE_COUNT_DETAIL')):
    """
    获取已结案件数
    :param product_code: 产品code
    :param start_date:开始日期
    :param end_date:结束日期
    :param age_start:年龄起始
    :param age_end:年龄结束
    :param sql: 查询sql
    :return:
    """

    if start_date and end_date:
        sql += f" where issue_time >= '{start_date}' and issue_time <= '{end_date}'"
    elif start_date:
        sql += f" where issue_time >= '{start_date}'"
    elif end_date:
        sql += f" where issue_time <= '{end_date}'"

    if age_start or age_end:
        if start_date or end_date:
            sql += " AND"
        else:
            sql += " WHERE"

    if age_start and age_end:
        sql += f" age >= {age_start} AND age <= {age_end}"
    elif age_start:
        sql += f" age >= {age_start}"
    elif age_end:
        sql += f" age <= {age_end}"
    if end_date is None:
        end_date = datetime.datetime.now().strftime('%Y-%m-%d')
    df_close_count = CONNECTOR.query(
        sql.format(product_code=product_code, end_date=end_date), show_spinner='查询中...',
        ttl=10)
    df_total_close_count = pd.DataFrame({'已完成结案次数': [df_close_count['id'].count()]})
    return df_total_close_count


def get_close_person(product_code, start_date=None, end_date=None, age_start=None, age_end=None,
                     sql=query_sql('SQL_CLOSE_PERSON_COUNT_DETAIL')):
    """
    获取已结案件人数
    :param product_code: 产品code
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param age_start: 年龄起始
    :param age_end: 年龄结束
    :param sql: 查询sql
    :return:
    """
    if start_date and end_date:
        sql += f" where issue_time >= '{start_date}' and issue_time <= '{end_date}'"
    elif start_date:
        sql += f" where issue_time >= '{start_date}'"
    elif end_date:
        sql += f" where issue_time <= '{end_date}'"

    if age_start or age_end:
        if start_date or end_date:
            sql += " AND"
        else:
            sql += " WHERE"

    if age_start and age_end:
        sql += f" age >= {age_start} AND age <= {age_end}"
    elif age_start:
        sql += f" age >= {age_start}"
    elif age_end:
        sql += f" age <= {age_end}"
    if end_date is None:
        end_date = datetime.datetime.now().strftime('%Y-%m-%d')
    df_close_person_count = CONNECTOR.query(
        sql.format(product_code=product_code, end_date=end_date), show_spinner='查询中...',
        ttl=10)

    df_total_close_person_count = pd.DataFrame({'已完成结案人数': [df_close_person_count['id'].nunique()]})
    return df_total_close_person_count


def get_claim_info(product_code, start_date=None, end_date=None, age_start=None, age_end=None):
    """
    获取理赔案件信息，将所有数据汇总
    :param product_code: 产品code
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param age_start: 年龄开始
    :param age_end: 年龄结束
    :return:
    """
    df_total_count = get_total_apply_count(product_code, start_date, end_date, age_start, age_end)
    df_total_person_count = get_total_apply_person(product_code, start_date, end_date, age_start, age_end)
    df_total_close_count = get_close_count(product_code, start_date, end_date, age_start, age_end)
    df_total_close_person_count = get_close_person(product_code, start_date, end_date, age_start, age_end)
    df_total_pay_info = get_pay_info(product_code, start_date, end_date, age_start, age_end)
    df_total_max_amount = get_total_max_amount(product_code, start_date, end_date, age_start, age_end)
    df_claim_info = pd.concat(
        [df_total_count, df_total_person_count, df_total_close_count, df_total_close_person_count, df_total_pay_info,
         df_total_max_amount], axis=1)
    return df_claim_info


def get_total_max_amount(product_code, start_date=None, end_date=None, age_start=None, age_end=None,
                         sql=query_sql('SQL_TOTAL_MAX_AMOUNT')):
    """
    获取最高赔付金额
    :param product_code: 产品chode
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param age_start: 年龄起始
    :param age_end: 年龄结束
    :param sql: 查询sql
    :return:
    """

    if start_date and end_date:
        sql += f" where issue_time >= '{start_date}' and issue_time <= '{end_date}'"
    elif start_date:
        sql += f" where issue_time >= '{start_date}'"
    elif end_date:
        sql += f" where issue_time <= '{end_date}'"

    if age_start or age_end:
        if start_date or end_date:
            sql += " AND"
        else:
            sql += " WHERE"

    if age_start and age_end:
        sql += f" age >= {age_start} AND age <= {age_end}"
    elif age_start:
        sql += f" age >= {age_start}"
    elif age_end:
        sql += f" age <= {age_end}"

    sql += " GROUP BY id_num order by sum(actual_amount)  desc limit 1"
    if end_date is None:
        end_date = datetime.datetime.now().strftime('%Y-%m-%d')
    df_total_max_amount = CONNECTOR.query(sql.format(product_code=product_code, end_date=end_date),
                                          show_spinner='查询中...', ttl=10)
    if df_total_max_amount.empty:
        return pd.DataFrame({'最高获赔金额': [0]})
    else:
        df_total_max_amount = pd.DataFrame({'最高获赔金额': [df_total_max_amount['max_pay_person'].values[0]]})
    return df_total_max_amount


def get_pay_info(product_code, start_date=None, end_date=None, age_start=None, age_end=None,
                 sql=query_sql('SQL_PAY_INFO_DETAIL')):
    """
    获取已结案件信息，包括理赔案件次数、理赔案件人数、赔付总金额
    :param product_code: 产品code
    :param start_date: 开始日期
    :param end_date: 结束日期
    :param age_start: 年龄起始
    :param age_end: 年龄结束
    :param sql: 查询sql
    :return:
    """
    if start_date and end_date:
        sql += f" where issue_time >= '{start_date}' and issue_time <= '{end_date}'"
    elif start_date:
        sql += f" where issue_time >= '{start_date}'"
    elif end_date:
        sql += f" where issue_time <= '{end_date}'"

    if age_start or age_end:
        if start_date or end_date:
            sql += " AND"
        else:
            sql += " WHERE"

    if age_start and age_end:
        sql += f" age >= {age_start} AND age <= {age_end}"
    elif age_start:
        sql += f" age >= {age_start}"
    elif age_end:
        sql += f" age <= {age_end}"
    if end_date is None:
        end_date = datetime.datetime.now().strftime('%Y-%m-%d')
    df_pay_info = CONNECTOR.query(sql.format(product_code=product_code, end_date=end_date), show_spinner='查询中...',
                                  ttl=10)
    df_total_pay_info = pd.DataFrame(
        {'理赔案件次数': [df_pay_info['id'].count()], '理赔案件人数': [df_pay_info['id_num'].nunique()],
         '赔付总金额': [df_pay_info['actual_amount'].sum()]})
    return df_total_pay_info


def get_claim_type_trend(product_code, conn=CONNECTOR):
    """
    获取理赔类型趋势(取近一周数据) 包括医疗、特药数据，不含质子重离子数据（无法确认日期）
    :param product_code:产品code
    :param conn:数据库连接
    :return:
    """
    df_med_claim_type_trend = conn.query(query_sql('SQL_MED_CLAIM_TYPE_TREND').
                                         format(product_code=product_code,
                                                start_date=(datetime.datetime.now() - datetime.timedelta(
                                                    days=7)).strftime(
                                                    '%Y-%m-%d'),
                                                end_date=datetime.datetime.now().strftime('%Y-%m-%d')),
                                         show_spinner='数据获取中...', ttl=10)

    df_drug_claim_type_trend = conn.query(query_sql('SQL_DRUG_CLAIM_TYPE_TREND').
                                          format(product_code=product_code,
                                                 start_date=(datetime.datetime.now() - datetime.timedelta(
                                                     days=7)).strftime(
                                                     '%Y-%m-%d'),
                                                 end_date=datetime.datetime.now().strftime('%Y-%m-%d')),
                                          show_spinner='数据获取中...', ttl=0)
    df_claim_type_trend = pd.concat([df_med_claim_type_trend, df_drug_claim_type_trend], axis=0)
    df_claim_type_trend['num'] = df_claim_type_trend['num'].astype(int)
    df_claim_type_trend = df_claim_type_trend.groupby(['issue_date', 'type']).sum().reset_index()
    df_claim_type_trend.rename(columns={'issue_date': '日期', 'type': '理赔类型', 'num': '数量'}, inplace=True)
    return df_claim_type_trend


def get_pay_response(product_code, product_serial_code, sale_amount, conn=CONNECTOR):
    """
    理赔赔付金额占比（分责任）
    :param product_code:产品code
    :param conn:数据库连接
    :return:
    """
    if product_serial_code in ['yichun_hmb', 'jiujiang_hxb', 'binzhou_yhb']:

        df_med_pay_response = conn.query(
            query_sql('SQL_MED_PAY_RESPONSE_V3').format(product_code=product_code, sale_amount=sale_amount,
                                                        end_time=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
            show_spinner='数据获取中...', ttl=10)
    else:
        df_med_pay_response = conn.query(
            query_sql('SQL_MED_PAY_RESPONSE_V4').format(product_code=product_code, sale_amount=sale_amount,
                                                        end_time=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
            show_spinner='数据获取中...', ttl=10)

    df_drug_pay_response = conn.query(
        query_sql('SQL_DRUG_PAY_AMOUNT').format(product_code=product_code,
                                                end_date=datetime.datetime.now().strftime('%Y-%m-%d')),
        show_spinner='数据获取中...', ttl=10)

    df_drug_pay_response.rename(columns={'赔付金额': '特药'}, inplace=True)
    df_pay_response = pd.concat([df_med_pay_response, df_drug_pay_response], axis=1)
    df_pay_response.fillna(0, inplace=True)
    df_pay_response['赔付金额'] = df_pay_response['赔付金额'] + df_pay_response['特药']
    df_pay_response.fillna(0, inplace=True)

    return df_pay_response


def multi_ring_chart(df_pst_symptom_copy):
    """
    绘制多层环形图
    :param df_pst_symptom_copy: 需要处理的dataframe
    :return: Pie对象
    """
    # 创建一个Pie对象
    pie = Pie()
    pie.set_colors(["#0068C9", "#83C9FF"])

    # 检查DataFrame是否为空
    if df_pst_symptom_copy.empty:
        # 如果DataFrame为空，使用默认数据
        columns = df_pst_symptom_copy.columns.tolist()
        if '分类' not in columns:
            raise ValueError("DataFrame 必须包含 '分类' 列")
        default_data = {
            '分类': ['既往症人数', '非既往症人数', '既往症赔付金额', '非既往症赔付金额']
        }
        for col in columns:
            if col != '分类':
                default_data[col] = [0, 0, 0, 0]
        df_pst_symptom_copy = pd.DataFrame(default_data)

    # 获取包含“人数”的列名
    person_columns = [col for col in df_pst_symptom_copy.columns if '分类' not in col]

    # 过滤出包含“人数”的行
    df_person = df_pst_symptom_copy[df_pst_symptom_copy['分类'].str.contains('人数')].reset_index(drop=True)

    # 添加每一层数据
    for i, col in enumerate(person_columns):
        data_pair = list(zip(df_person['分类'], df_person[col]))
        radius = [20 + i * 20, 40 + i * 20]
        label_formatter = f"{col}{{b}}: {{c}} ({{d}}%)" if col != '合计' else "{b}: {c} ({d}%)"
        tooltip_formatter = f"{col}<br/>{{b}} : {{c}} ({{d}}%)" if col != '合计' else "合计<br/>{b} : {c} ({d}%)"

        pie.add(
            series_name="",
            data_pair=data_pair,
            radius=radius,
            label_opts=opts.LabelOpts(formatter=label_formatter, position='outside'),
            tooltip_opts=opts.TooltipOpts(formatter=tooltip_formatter)
        )

    return pie


def get_pst_symptom_info(product_code, product_serial_code, conn=CONNECTOR):
    """
    获取既往症信息
    :param product_code:产品code
    :param conn:数据库连接
    :return:
    """
    if product_serial_code in ['yichun_hmb', 'jiujiang_hxb', 'binzhou_yhb']:

        df_med_pst_symptom_info = conn.query(
            query_sql('SQL_MED_PAST_SYMPTOM').format(product_code=product_code,
                                                     end_date=datetime.datetime.now().strftime('%Y-%m-%d')),
            show_spinner='数据获取中...', ttl=10)
    else:
        df_med_pst_symptom_info = conn.query(
            query_sql('SQL_MED_PAST_SYMPTOM_V4_1').format(product_code=product_code,
                                                          end_date=datetime.datetime.now().strftime('%Y-%m-%d')),
            show_spinner='数据获取中...', ttl=10)

    df_drug_pst_symptom_info = conn.query(
        query_sql('SQL_DRUG_CLAIM_PAST_SYMPTOM').format(product_code=product_code,
                                                        end_date=datetime.datetime.now().strftime('%Y-%m-%d')),
        show_spinner='数据获取中...', ttl=10)
    # 医惠保责任较多，需要特殊处理（医疗、特药都是医疗的表中），单独处理逻辑
    if product_serial_code in ['jiangsu_yhb']:
        df_person_columns = df_med_pst_symptom_info.filter(like='人数')
        df_past_symptom_column = df_med_pst_symptom_info[['是否既往症']]

        # 合并两个 DataFrame
        df_med_pst_symptom_person = pd.concat([df_person_columns, df_past_symptom_column], axis=1)
        df_med_pst_symptom_person.rename(
            columns={'是否既往症': '分类', '医惠保医疗赔付人数': '医疗', '责任五赔付人数': '质子重离子',
                     '责任六赔付人数': 'CAR-T药', '赔付人数': '合计'}, inplace=True)
        df_amount_columns = df_med_pst_symptom_info.filter(like='金额')
        df_med_pst_symptom_amount = pd.concat([df_amount_columns, df_past_symptom_column], axis=1)
        df_med_pst_symptom_amount['医惠保医疗赔付金额'] = df_med_pst_symptom_amount['责任一赔付金额'] + \
                                                          df_med_pst_symptom_amount['责任二赔付金额'] + \
                                                          df_med_pst_symptom_amount['责任三赔付金额'] + \
                                                          df_med_pst_symptom_amount['责任四赔付金额']
        df_med_pst_symptom_amount.rename(
            columns={'是否既往症': '分类', '医惠保医疗赔付金额': '医疗', '责任五赔付金额': '质子重离子',
                     '责任六赔付金额': 'CAR-T药',
                     '赔付金额': '合计'}, inplace=True)
        df_med_pst_symptom = pd.concat([df_med_pst_symptom_person[['分类', '医疗', '质子重离子', 'CAR-T药', '合计']].replace(['既往症', '非既往症'],
                                                                                                ['既往症人数',
                                                                                                 '非既往症人数']),
                                        df_med_pst_symptom_amount[['分类', '医疗', '质子重离子', 'CAR-T药', '合计']].replace(['既往症', '非既往症'],
                                                                                                ['既往症金额',
                                                                                                 '非既往症金额'])],
                                       axis=0)
        return df_med_pst_symptom

    df_med_pst_symptom = pd.concat([df_med_pst_symptom_info[['是否既往症', '赔付人数']].replace(['既往症', '非既往症'],
                                                                                                ['既往症人数',
                                                                                                 '非既往症人数']).rename(
        columns={'是否既往症': '分类', '赔付人数': '医疗'}),
        df_med_pst_symptom_info[['是否既往症', '赔付金额']].replace(['既往症', '非既往症'],
                                                                    ['既往症赔付金额', '非既往症赔付金额']).rename(
            columns={'是否既往症': '分类', '赔付金额': '医疗'})],
        axis=0)
    df_drug_pst_symptom = pd.concat(
        [df_drug_pst_symptom_info[['是否既往症', '赔付人数']].replace(['既往症', '非既往症'],
                                                                      ['既往症人数', '非既往症人数']).rename(
            columns={'是否既往症': '分类', '赔付人数': '特药'}),
            df_drug_pst_symptom_info[['是否既往症', '赔付金额']].replace(['既往症', '非既往症'],
                                                                         ['既往症赔付金额', '非既往症赔付金额']).rename(
                columns={'是否既往症': '分类', '赔付金额': '特药'})], axis=0)
    df_pst_symptom = pd.merge(df_med_pst_symptom, df_drug_pst_symptom, on='分类', how='outer')
    # print(df_pst_symptom)

    df_pst_symptom['合计'] = df_pst_symptom[['医疗', '特药']].sum(axis=1)
    df_pst_symptom.fillna(0, inplace=True)
    # 中文指定排序
    chinese_sort = ['既往症人数', '非既往症人数', '既往症赔付金额', '非既往症赔付金额']
    # 按照中文指定顺序排序
    df_pst_symptom['分类'] = pd.Categorical(df_pst_symptom['分类'], categories=chinese_sort, ordered=True)
    df_pst_symptom.sort_values(by='分类', ascending=True, inplace=True)
    df_pst_symptom.reset_index(drop=True, inplace=True)

    # 剩余的只有医疗的责任一、二，也就是医疗理赔，没有特药，需要删除
    if product_serial_code not in ['jiujiang_hxb', 'yichun_hmb']:
        if '特药' in df_pst_symptom.columns:
            df_pst_symptom.drop(columns=['特药'], inplace=True)
    return df_pst_symptom


def get_audit_time_med(product_code, product_serial_code, conn=CONNECTOR):
    """
    获取理赔审核时间分布
    :param product_code:产品code
    :param conn:数据库连接
    """
    if product_serial_code in ['yichun_hmb', 'jiujiang_hxb', 'binzhou_yhb']:
        df_audit_time_med = conn.query(
            query_sql('SQL_AUDIT_TIME_MED_V3').format(product_code=product_code,
                                                      end_date=datetime.datetime.now().strftime('%Y-%m-%d')),
            show_spinner='数据获取中...', ttl=10)
    else:
        df_audit_time_med = conn.query(
            query_sql('SQL_AUDIT_TIME_MED_V4').format(product_code=product_code,
                                                      end_date=datetime.datetime.now().strftime('%Y-%m-%d')),
            show_spinner='数据获取中...', ttl=10)
    df_audit_time_med.rename(columns={'days': '天数', 'num': '审核件数'}, inplace=True)
    # 天数小于0的，设置为0天
    df_audit_time_med.loc[df_audit_time_med['天数'] < 0, '天数'] = 0
    df_audit_time_med = df_audit_time_med.groupby(['天数'])['审核件数'].sum().reset_index()
    df_audit_time_med['审核天数'] = df_audit_time_med['天数'].apply(lambda x: str(x) + '天')
    df_audit_time_med_t = df_audit_time_med[['审核天数', '审核件数']].T.reset_index()
    # 设置审核天数行为列名
    df_audit_time_med_t.columns = df_audit_time_med_t.iloc[0, :]
    df_audit_time_med_t.drop(df_audit_time_med_t.index[0], inplace=True)
    df_audit_time_med_t.reset_index(drop=True, inplace=True)
    df_audit_time_med_t.set_index('审核天数', inplace=True)
    return df_audit_time_med, df_audit_time_med_t


def st_tabel_1(product_code, product_code_shift, sale_from, sale_until, sale_amount, sale_amount_shift):
    """
    生成表格1:理赔案件情况
    :param product_code:产品code
    :param product_code_shift:上年产品code
    :param product_info_index:产品信息索引
    :param sale_from:开始日期
    :param sale_until:结束日期
    :param sale_amount:当年销售金额
    :param sale_amount_shift:上年销售金额
    :param product_info:产品信息
    :return:
    """
    st.divider()
    text_write('理赔案件情况')
    cols = st.columns(5)
    with cols[0]:
        start_date = st.date_input('开始日期', min_value=sale_from, max_value=sale_until, value=sale_from,
                                   key='start_date')

    with cols[1]:
        end_date = st.date_input('结束日期', min_value=sale_from, max_value=sale_until, value=sale_until,
                                 key='end_date')

    with cols[2]:
        age_start = st.text_input("最小年龄")

    with cols[3]:
        age_end = st.text_input("最大年龄")

    # submitted = st.button("查询")
    #
    # if submitted:
    if start_date and end_date and start_date > end_date:
        st.error('开始日期不能大于结束日期')
    elif age_start and age_end and int(age_start) > int(age_end):
        st.error('最小年龄不能大于最大年龄')
    else:
        df_claim_info = get_claim_info(product_code, start_date, end_date, age_start, age_end)
        # 理赔申请数量
        apply_num = df_claim_info.loc[0, '客户理赔申请案件次数']
        # 理赔申请人数
        apply_person_num = df_claim_info.loc[0, '客户理赔申请案件人数']
        # 理赔结案数量
        close_num = df_claim_info.loc[0, '已完成结案次数']
        # 理赔结案人数
        close_person_num = df_claim_info.loc[0, '已完成结案人数']
        # 理赔数量
        claim_num = df_claim_info.loc[0, '理赔案件次数']
        # 理赔人数
        claim_person_num = df_claim_info.loc[0, '理赔案件人数']
        # 理赔金额
        claim_amount = df_claim_info.loc[0, '赔付总金额']
        # 理赔率
        claim_rate = str(round(claim_amount / sale_amount * 100, 2)) + '%'
        # 上期同期理赔率
        if product_code_shift is not None and pd.notna(product_code_shift):
            start_date_shift, end_date_shift = get_shifted_dates(start_date, end_date)
            pay_info_shift = get_pay_info(product_code_shift, start_date_shift, end_date_shift, age_start, age_end)
            claim_rate_shift = str(round((pay_info_shift.loc[0, '赔付总金额']) / sale_amount_shift * 100, 2)) + '%'
        else:
            claim_rate_shift = '未知'
        # 人均赔付金额
        # 这边无法通过身份证号码去重，有一定的问题
        pay_money_person = (claim_amount / (claim_person_num)).round(2)
        # 件均赔付金额
        pay_money_claim = (claim_amount / claim_num).round(2)
        # 最高获赔金额
        max_pay_amount = df_claim_info.loc[0, '最高获赔金额']
        df_new = pd.DataFrame({'客户理赔申请案件次数': [apply_num], '客户理赔申请案件人数 ': [apply_person_num],
                               '已完成结案次数': [close_num],
                               '已完成结案人数': [close_person_num], '理赔案件次数': [claim_num],
                               '理赔案件人数': [claim_person_num], '赔付总金额': [claim_amount],
                               '当期赔付率': [claim_rate], '上期同期赔付率': [claim_rate_shift],
                               '件均赔付金额': [pay_money_claim],
                               '人均赔付金额': [pay_money_person], '最高获赔金额': [max_pay_amount]})

        # 展示DataFrame，并应用样式
        st.dataframe(df_new, use_container_width=True, hide_index=True)


def st_chart_3(product_code):
    """
    绘制近一周申请单量
    :param product_code:
    :return:
    """
    st.divider()
    text_write('近一周申请单量')
    df_claim_type_trend = get_claim_type_trend(product_code)
    # 使用plotly.express绘制条形图
    fig = px.bar(df_claim_type_trend, x="日期", y="数量", color="理赔类型", barmode="group", text_auto=True,
                 height=360)
    # 设置横坐标日期显示格式为mm-dd
    fig.update_xaxes(
        tickformat='%m-%d'  # 设置日期格式
    )
    # 更新文本位置和样式
    fig.update_traces(
        textposition='outside'  # 文本显示在柱子外面
    )
    # 隐藏横纵坐标轴上的标签
    fig.update_layout(
        xaxis_title='',
        yaxis_title='',
        bargap=0.5,
        legend_title=""
    )
    st.plotly_chart(fig)


def st_chart_4(product_code, product_serial_code, sale_amount):
    """
    绘制理赔赔付金额占比饼图，理赔赔付率、理赔总额仪表盘，以及总费用、个人自付、门诊日间手术、个人自费、赔付总额、个人自费赔付额、门诊日间手术赔付额值
    :param product_code: 产品code
    :param sale_amount: 销售金额（总保费）
    :return:
    """
    st.divider()
    text_write('理算赔付金额占比（单位:元）')
    df_pay_response = get_pay_response(product_code, product_serial_code, sale_amount)
    if product_serial_code in ['yichun_hmb', 'jiujiang_hxb', 'binzhou_yhb']:
        df_pay_response_t = df_pay_response[
            ['责任一', '责任二', '特药']].T.reset_index()
    elif product_serial_code in ['neimenggu_hmb', 'xiantao_xhb', 'hebei_jhb', 'shanxi_jkb', 'hunan_amb']:
        df_pay_response_t = df_pay_response[
            ['责任一', '责任二']].T.reset_index()
    elif product_serial_code in ['jiangsu_yhb']:
        df_pay_response_t = df_pay_response[
            ['责任一', '责任二', '责任三', '责任四', '责任五', '责任六']].T.reset_index()
    df_pay_response_t.columns = ['分类', '金额']
    # 使用plotly.express绘制环形图
    fig = px.pie(df_pay_response_t, values='金额', names='分类', height=365, category_orders=
    {'分类': ['责任一', '责任二', '责任三', '责任四', '责任五', '责任六', '特药']})
    # 在环形图中心添加文字
    fig.add_annotation(
        dict(showarrow=False, text="已赔付总金额<br>%s" % str(round(df_pay_response['赔付金额'].values[0], 2)), x=0.5,
             y=0.5))
    # 调整环形大小及显示格式
    fig.update_traces(textinfo='value+percent+label', texttemplate='%{label}<br>%{value} (%{percent})',
                      textposition='outside', automargin=True, hole=0.65)
    st.plotly_chart(fig)


def st_chart_7(product_code, product_serial_code):
    """
    绘制既往症理赔组合图（条形图-理赔金额，折线图-理赔率）、饼图（赔付金额占比）
    :param product_code: 产品code
    :param sale_amount: 销售金额（总保费）
    """
    st.divider()
    text_write('既往症理赔情况')
    cols = st.columns([0.25, 0.25])
    df_pst_symptom = get_pst_symptom_info(product_code, product_serial_code)
    with cols[0]:
        # 绘制多层环形图
        df_pst_symptom_copy = df_pst_symptom.copy()
        pie = multi_ring_chart(df_pst_symptom_copy)
        st_pyecharts(pie, key="pie_chart1")
    with cols[1]:
        # 既往症赔付金额、人数数据dataframe
        empty_line(4)
        st.dataframe(df_pst_symptom, use_container_width=True, hide_index=True)


def st_chart_10(product_code, product_serial_code):
    st.divider()
    text_write("近一月智慧医疗审核时长")
    df_audit_time_med, df_audit_time_med_t = get_audit_time_med(product_code, product_serial_code)
    cols = st.columns([0.3, 0.3, 0.3, 0.3, 0.1])
    # 如果为空，计算均值会报错，所以先判断是否为空
    if df_audit_time_med.empty:
        max_day = 0
        mean_day = 0
        min_day = 0
    else:
        max_day = df_audit_time_med['天数'].max()
        mean_day = int(
            sum(df_audit_time_med['天数'] * df_audit_time_med['审核件数']) / sum(df_audit_time_med['审核件数']))
        min_day = df_audit_time_med['天数'].min()

    with cols[1]:
        st.metric('最短审核天数', min_day)
    with cols[2]:
        st.metric('平均审核天数', mean_day)
    with cols[3]:
        st.metric('最长审核天数', max_day)

    st.line_chart(df_audit_time_med, x='天数', y='审核件数', use_container_width=True)
    st.dataframe(df_audit_time_med_t, use_container_width=True)


def get_app_mapping():
    """
    获取理赔应用的对应关系数据
    :return:
    """
    sql = """
    select product_serial_code,client_id,url from other_streamlit_key_mapping where type='claim'
    """
    df_app_mapping = CONNECTOR_DW.query(sql)
    return df_app_mapping

def main():
    # 权限检查
    is_iframe, user_permission, disable, product_serial_code_iframe, product_set_code_iframe = agent_auth_check()
    person_info = auth_from_session()
    st.subheader("理赔报表")
    product_info = get_product_code()
    df_app_mapping = get_app_mapping()

    # 选择日期
    sale_from = datetime.date(2021, 1, 1)
    sale_until = datetime.date.today()
    # send_feishu_message(product_set_code_iframe)
    if product_set_code_iframe in product_info['product_set_code'].values or product_set_code_iframe is None:
        cols = st.columns(3)
        with cols[0]:
            # 如果是权利端来的，而且有产品名称，则默认显示该产品名称，否则控件放开
            if is_iframe == 1 and product_set_code_iframe is not None:
                product_set_name_iframe = \
                    product_info[product_info['product_set_code'] == product_set_code_iframe][
                        'product_set_name'].values[0]
                column_name = st.selectbox("请选择产品", options=[product_set_name_iframe],
                                           placeholder="请选择产品", disabled=True)
                if user_permission == 1:
                    client_id = df_app_mapping[df_app_mapping['product_serial_code'] == product_serial_code_iframe][
                        'client_id'].values[
                        0]
                    url = \
                    df_app_mapping[df_app_mapping['product_serial_code'] == product_serial_code_iframe]['url'].values[0]
                    url = quote(url, safe='')
                    query_url = f"https://maxkey.njzhyl.cn/sign/authz/oauth/v20/authorize?client_id={client_id}&response_type=code&redirect_uri={url}&approval_prompt=auto"
                    st.link_button("跳转详细报表",query_url,icon="🔥")
            # 限制圆心账号的查询范围，防止后续放开产品集的取数范围，不限制导致后续有问题
            elif person_info.get('displayName') in ['yx001','yx002']:
                # 限制product_serial_code 在'neimenggu_hmb', 'xiantao_xhb', 'shanxi_jkb', 'hunan_amb'中
                product_info_v1 = product_info[product_info['product_serial_code'].isin(['neimenggu_hmb', 'xiantao_xhb','shanxi_jkb', 'hunan_amb'])]
                column_name = st.selectbox("请选择产品", index=0, options=product_info_v1['product_set_name'],
                                           placeholder="请选择产品")

            else:
                column_name = st.selectbox("请选择产品", index=0, options=product_info['product_set_name'],
                                           placeholder="请选择产品")
                product_info_index = product_info[product_info['product_set_name'] == column_name].index[0]
                product_serial_code = product_info.loc[product_info_index, 'product_serial_code']
                # print(product_serial_code)
                client_id = \
                df_app_mapping[df_app_mapping['product_serial_code'] == product_serial_code]['client_id'].values[
                    0]
                url = df_app_mapping[df_app_mapping['product_serial_code'] == product_serial_code]['url'].values[
                    0]
                url = quote(url, safe='')
                query_url = f"https://maxkey.njzhyl.cn/sign/authz/oauth/v20/authorize?client_id={client_id}&response_type=code&redirect_uri={url}&approval_prompt=auto"
                # print(query_url)
                st.link_button("跳转详细报表",query_url,icon="🔥")
            if column_name:
                product_set_code = \
                    product_info[product_info['product_set_name'] == column_name]['product_set_code'].values[0]
                product_info_index = product_info[product_info['product_set_name'] == column_name].index[0]
                product_code = product_info.loc[product_info_index, 'product_code']
                product_code_shift = product_info.loc[product_info_index, 'product_code_shift']
                sale_amount = product_info.loc[product_info_index, 'amount']
                sale_amount_shift = product_info.loc[product_info_index, 'amount_shift']  # 产品销售额的上一期
                product_serial_code = product_info.loc[product_info_index, 'product_serial_code']
            else:
                product_set_code = None
                product_code = None
                product_code_shift = None
                sale_amount = None
                sale_amount_shift = None
                product_serial_code = None
        if product_code:
            st_tabel_1(product_code, product_code_shift, sale_from, sale_until, sale_amount, sale_amount_shift)
            st_chart_3(product_code)
            st_chart_4(product_code, product_serial_code, sale_amount)
            st_chart_7(product_code, product_serial_code)
            if person_info.get('displayName') not in ['yx001','yx002']:
                st_chart_10(product_code, product_serial_code)
        else:
            st.info('请选择产品')
    else:
        st.info('未找到产品信息')


if __name__ == '__main__':
    main()
