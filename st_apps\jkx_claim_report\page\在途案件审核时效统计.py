import datetime as dt
import warnings
import time
import pymysql
import numpy as np
import idna
import pandas as pd
import streamlit as st
from utils.st import query_sql,text_write
from utils.utils import sum_or_combine, replace_using_dict
from utils.auth import agent_auth_check

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)

CONNECTOR = st.connection('claim', type='sql')

def get_product_code():
    """
    获取产品代码、金额等信息
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
        REPLACE ( ps.NAME, pss.NAME, '' ) product_short_name,
        ps.NAME product_set_name,
        ps.CODE product_set_code,
        GROUP_CONCAT( p.CODE SEPARATOR ',' ) product_code,
        valid_from ,
        pss.code product_serial_code,
        pss.name product_serial_name
    FROM
        product p
        JOIN product_set ps ON p.product_set_code = ps.
        CODE JOIN product_serial pss ON ps.product_serial_code = pss.CODE
    where
    pss.code IN ( 'hunan_amb', 'neimenggu_hmb', 'shanxi_jkb', 'xiantao_xhb' )
    GROUP BY
        ps.code
    ORDER BY
        pss.CODE,
        ps.CODE desc,
        valid_from DESC
        '''

    df_product_code = CONNECTOR.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=10)
    df_product_code['product_code_list'] = df_product_code['product_code'].apply(lambda x: x.split(','))
    # 将product_code中的字符串用单引号包裹
    df_product_code['product_code'] = df_product_code['product_code_list'].apply(
        lambda x: ','.join([f"'{i}'" for i in x]))
    df_product_code.drop(columns=['product_code_list'], inplace=True)

    df_product_code['product_code_shift'] = df_product_code.groupby('product_serial_code')['product_code'].shift(-1)
    df_product_code['product_name_shift'] = df_product_code.groupby('product_serial_code')['product_set_name'].shift(-1)
    df_product_code.fillna(value=np.nan, inplace=True)
    return df_product_code



def get_initial_status_change_cases(product_set_code,end_date,sql = query_sql('SQL_INITIAL_STATUS_CHANGE_PASS_DAY')):
    """
    获取转状态案件数据
    :param product_set_code: 产品集代码
    :param end_date: 截止日期
    :return: DataFrame
    """
    df = CONNECTOR.query(sql.format(product_set_code=product_set_code,end_date=end_date), ttl=10)
    df['类型'] = '转状态案件'
    return df

def get_initial_new_cases(product_set_code,end_date,sql = query_sql('SQL_INITIAL_NEW_CASE_PASS_DAY')):
    """
    获取转状态案件数据
    :param product_set_code: 产品集代码
    :param end_date: 截止日期
    :return: DataFrame
    """
    df = CONNECTOR.query(sql.format(product_set_code=product_set_code,end_date=end_date), ttl=10)
    df['类型'] = '新案件'
    return df


def get_review_status_change_cases(product_set_code,end_date,sql = query_sql('SQL_REVIEW_STATUS_CHANGE_PASS_DAY')):
    """
    获取转状态案件数据
    :param product_set_code: 产品集代码
    :param end_date: 截止日期
    :return: DataFrame
    """
    df = CONNECTOR.query(sql.format(product_set_code=product_set_code,end_date=end_date), ttl=10)
    df['类型'] = '转状态案件'
    return df

def get_review_new_cases(product_set_code,end_date,sql = query_sql('SQL_REVIEW_NEW_CASE_PASS_DAY')):
    """
    获取转状态案件数据
    :param product_set_code: 产品集代码
    :param end_date: 截止日期
    :return: DataFrame
    """
    df = CONNECTOR.query(sql.format(product_set_code=product_set_code,end_date=end_date), ttl=10)
    df['类型'] = '新案件'
    return df


def process_data(status_change_cases, new_cases, product_serial_name_list):
    """处理案件数据的通用函数
    :param status_change_cases: 转状态案件数据
    :param new_cases: 新案件数据
    :param product_serial_name_list: 产品系列名称列表
    :return: 处理后的DataFrame
    """
    # 合并数据
    df_data = pd.concat([status_change_cases, new_cases], axis=0)
    
    # 创建透视表
    df_pivot = df_data.pivot_table(
        index=['产品系列', '类型'],
        columns='超时效',
        values='数量',
        aggfunc='sum'
    ).reset_index()
    
    # 处理多级索引列名
    if isinstance(df_pivot.columns, pd.MultiIndex):
        df_pivot.columns = ['产品系列' if col[0] == '产品系列' else
                           '类型' if col[0] == '类型' else
                           col[1] for col in df_pivot.columns]
    
    # 创建完整的产品系列和类型组合模板
    type_list = ['转状态案件', '新案件']
    df_template = pd.DataFrame(
        [(series, type_) for series in product_serial_name_list for type_ in type_list],
        columns=['产品系列', '类型']
    )
    
    # 合并数据并填充缺失值
    df_result = pd.merge(df_template, df_pivot, on=['产品系列', '类型'], how='left')
    df_result.fillna(0, inplace=True)
    
    # 确保包含所有必要的列
    required_columns = ['T+0', '超T+1', '超T+2', '超T+3', '超T+4', '超T+5']
    for col in required_columns:
        if col not in df_result.columns:
            df_result[col] = 0
    
    return df_result

def get_initial_data(product_set_code, product_serial_name_list, end_date):
    """获取初审数据
    :param product_set_code: 产品集代码
    :param product_serial_name_list: 产品系列名称列表
    :param end_date: 截止日期
    :return: DataFrame
    """
    df_initial_status_change_cases = get_initial_status_change_cases(product_set_code, end_date)
    df_initial_new_cases = get_initial_new_cases(product_set_code, end_date)
    return process_data(df_initial_status_change_cases, df_initial_new_cases, product_serial_name_list)

def get_review_data(product_set_code, product_serial_name_list, end_date):
    """获取复审数据
    :param product_set_code: 产品集代码
    :param product_serial_name_list: 产品系列名称列表
    :param end_date: 截止日期
    :return: DataFrame
    """
    df_review_status_change_cases = get_review_status_change_cases(product_set_code, end_date)
    df_review_new_cases = get_review_new_cases(product_set_code, end_date)
    return process_data(df_review_status_change_cases, df_review_new_cases, product_serial_name_list)


def get_total_data(product_set_code, product_serial_name_list, end_date):
    """获取总数据
    :param product_set_code: 产品集code
    :param product_serial_name_list: 产品系列名称列表
    :param end_date: 截止日期
    :return: DataFrame
    """
    # 获取数据
    df_initial_data = get_initial_data(product_set_code, product_serial_name_list, end_date)
    df_review_data = get_review_data(product_set_code, product_serial_name_list, end_date)

    # 为每个DataFrame添加审核类型列并计算每个产品系列的总和
    df_initial_data['审核类型'] = '初审'
    df_review_data['审核类型'] = '复审'

    # 获取所有数值列
    value_columns = [col for col in df_initial_data.columns if col not in ['产品系列', '类型', '审核类型']]

    # 对每个数据集按产品系列分组并计算总和
    df_initial_sum = df_initial_data.groupby('产品系列')[value_columns].sum().reset_index()
    df_initial_sum['审核类型'] = '初审'
    
    df_review_sum = df_review_data.groupby('产品系列')[value_columns].sum().reset_index()
    df_review_sum['审核类型'] = '复审'

    # 合并初审和复审数据
    df_combined = pd.concat([df_initial_sum, df_review_sum])

    # 计算合计数据
    df_total = df_combined.groupby('产品系列')[value_columns].sum().reset_index()
    df_total['审核类型'] = '合计'

    # 合并所有数据
    df_result = pd.concat([df_total, df_initial_sum, df_review_sum])

    # 确保包含所有必要的列
    required_columns = ['T+0', '超T+1', '超T+2', '超T+3', '超T+4', '超T+5']
    for col in required_columns:
        if col not in df_result.columns:
            df_result[col] = 0
            if col not in value_columns:
                value_columns.append(col)

    # 计算超时效单量
    df_result['超时效单量'] = df_result[value_columns].sum(axis=1)

    # 确保包含所有产品系列
    all_combinations = pd.DataFrame([(series, audit_type) 
                                   for series in product_serial_name_list 
                                   for audit_type in ['合计', '初审', '复审']],
                                  columns=['产品系列', '审核类型'])

    df_result = pd.merge(all_combinations, df_result, on=['产品系列', '审核类型'], how='left')
    df_result.fillna(0, inplace=True)

    # 列排序
    df_result = df_result[['产品系列', '审核类型', '超时效单量'] + required_columns]
    
    return df_result




def main():
    is_iframe, user_permission, disable, product_serial_code_iframe, product_set_code_iframe = agent_auth_check()
    st.subheader("在途案件审核时效统计")
    product_info = get_product_code()
    if product_serial_code_iframe in product_info['product_serial_code'].values or product_serial_code_iframe is None:
        if is_iframe == 1 and product_serial_code_iframe is not None:
            iframe_product_serial_name = \
            product_info[product_info['product_serial_code'] == product_serial_code_iframe][
                'product_serial_name'].values[0]
            product_serial_name_input = st.multiselect('产品系列', product_info['product_serial_name'].tolist(),
                                                 default=[iframe_product_serial_name],disabled=True)
        else:
            product_serial_name_input = st.multiselect(
                "请选择产品系列",product_info["product_serial_name"].unique(),default=product_info["product_serial_name"].unique(),
                key = 'product_serial_name_input',placeholder = '请选择产品系列')
        if not product_serial_name_input:
            st.warning("请选择产品系列")
            return
        if product_serial_name_input:
            product_info = product_info[product_info["product_serial_name"].isin(product_serial_name_input)]

        # 产品集筛选
        if is_iframe == 1 and product_set_code_iframe is not None:
            product_name_input = st.multiselect('请选择产品', product_info['product_set_name'].tolist(),
                                                 default=[product_info[product_info['product_set_code'] == product_set_code_iframe]['product_set_name'].values[0]],disabled=True)
        else:
            product_name_input = st.multiselect(
                "请选择产品",product_info["product_set_name"].tolist(),placeholder = '请选择产品',key = 'product_name_input',
                help = '不选择产品，默认选择全部产品')

        if not product_name_input:
            product_name_input = product_info["product_set_name"].tolist()
        product_set_code = product_info[product_info["product_set_name"].isin(product_name_input)]["product_set_code"].tolist()
        product_set_str = "','".join(product_set_code)
        sale_from = dt.date(2000, 1, 1)
        sale_until = dt.date.today()

        end_date = st.date_input('截止日期', min_value=sale_from, max_value=sale_until, value=sale_until)

        if end_date is None:
            end_date = sale_until
        st.divider()

        # 获取合计数据
        df_total = get_total_data(product_set_str, product_serial_name_input, end_date)

        st.dataframe(df_total, hide_index=True, use_container_width=True,height=458)

    else:
        st.info('未找到产品信息')


if __name__ == '__main__':
    main()
