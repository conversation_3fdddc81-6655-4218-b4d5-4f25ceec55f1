{% extends 'public/data_dictionary/base.html' %}
{% load static %}

{% block title %}数据补充 - 数据字典{% endblock %}

{% block extrahead %}
<style>
    /* 现代化设计风格 - 参考test.html */
    * {
        box-sizing: border-box;
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: #f8fafc;
        color: #334155;
        line-height: 1.6;
    }

    .supplement-container {
        padding: 24px;
        background: #f8fafc;
        min-height: calc(100vh - 120px);
        max-width: 1400px;
        margin: 0 auto;
    }

    .supplement-header {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 24px;
    }

    .supplement-header h1 {
        font-size: 24px;
        font-weight: 600;
        color: #111827;
        margin: 0 0 4px 0;
    }

    .supplement-header p {
        color: #6b7280;
        font-size: 14px;
        margin: 0;
    }

    .confidence-legend {
        display: flex;
        align-items: center;
        gap: 24px;
        padding: 12px 16px;
        background: #f8fafc;
        border-radius: 6px;
        margin-top: 20px;
        margin-bottom: 20px;
        font-size: 13px;
    }

    .confidence-legend .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .confidence-legend .color-box {
        width: 8px;
        height: 8px;
        border-radius: 50%;
    }

    .confidence-high-color { background-color: #10b981; }
    .confidence-medium-color { background-color: #f59e0b; }
    .confidence-low-color { background-color: #ef4444; }

    .filter-section {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .controls-row {
        display: flex;
        gap: 16px;
        align-items: end;
        flex-wrap: wrap;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        min-width: 150px;
    }

    .form-label {
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        margin-bottom: 6px;
    }

    .form-select, .form-control {
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        background: white;
        transition: border-color 0.2s;
        height: 38px;
    }

    .form-select:focus, .form-control:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .btn {
        padding: 8px 16px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: white;
        color: #374151;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 6px;
        text-decoration: none;
        height: 38px;
        justify-content: center;
    }

    .btn:hover {
        border-color: #9ca3af;
        background: #f9fafb;
        text-decoration: none;
        color: #374151;
    }

    .btn-primary {
        background: #3b82f6;
        color: white;
        border-color: #3b82f6;
    }

    .btn-primary:hover {
        background: #2563eb;
        border-color: #2563eb;
        color: white;
    }

    .btn-group {
        display: flex;
        gap: 4px;
        flex-wrap: nowrap;
        align-items: center;
        white-space: nowrap;
    }

    .btn-group-sm .btn {
        padding: 4px 8px;
        font-size: 12px;
        height: auto;
        min-width: 60px;
        line-height: 1.4;
        vertical-align: middle;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
    }

    /* 表中文名缺失页面的保存按钮样式 */
    .btn-outline-primary {
        white-space: nowrap;
        min-width: 60px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 6px 12px;
    }

    .nav-tabs {
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 0;
    }

    .nav-tabs .nav-link {
        border: none;
        background: transparent;
        color: #6b7280;
        margin-right: 2px;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 14px;
        transition: all 0.2s;
    }

    .nav-tabs .nav-link.active {
        background: #eff6ff;
        color: #3b82f6;
        font-weight: 500;
    }

    .nav-tabs .nav-link:hover {
        color: #3b82f6;
        background: #eff6ff;
    }

    .data-section {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        overflow: hidden;
        width: 100%;
    }

    .section-header {
        padding: 16px 20px;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .section-tabs {
        display: flex;
        gap: 2px;
    }

    .tab {
        padding: 8px 16px;
        border: none;
        background: transparent;
        color: #6b7280;
        font-size: 14px;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .tab.active {
        background: #eff6ff;
        color: #3b82f6;
        font-weight: 500;
    }

    .tab:hover {
        color: #3b82f6;
        background: #f8fafc;
    }

    .table-responsive {
        width: 100%;
        overflow-x: auto;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }

    .data-table th {
        background: #f8fafc;
        color: #374151;
        font-weight: 500;
        font-size: 13px;
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
        white-space: nowrap;
    }

    .data-table td {
        padding: 12px 16px;
        border-bottom: 1px solid #f1f5f9;
        font-size: 14px;
        vertical-align: middle;
    }

    .data-table tr:hover {
        background: #f8fafc;
    }

    .comment-input {
        padding: 6px 8px;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        font-size: 13px;
        width: auto;
        min-width: 100px;
        max-width: 180px;
        flex: 1;
        background: #fafafa;
    }

    .comment-input:focus {
        outline: none;
        border-color: #3b82f6;
        background: white;
    }

    .btn-save-batch {
        background: #10b981;
        border: 1px solid #10b981;
        color: white;
        border-radius: 6px;
        font-weight: 500;
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        transition: all 0.2s;
    }

    .btn-save-batch:hover {
        background: #059669;
        border-color: #059669;
        color: white;
    }

    .stats-badge {
        background: #3b82f6;
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .database-badge {
        display: inline-block;
        padding: 2px 8px;
        background: #3b82f6;
        color: white;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .field-name {
        color: #3b82f6;
        font-weight: 500;
    }

    .table-name {
        color: #374151;
        font-weight: 500;
    }

    .column-name {
        color: #3b82f6;
        font-weight: 500;
    }

    /* 按钮样式优化 */
    .btn-outline-success {
        border-color: #10b981;
        color: #10b981;
    }

    .btn-outline-success:hover {
        background-color: #10b981;
        border-color: #10b981;
        color: white;
    }

    .btn-outline-danger {
        border-color: #ef4444;
        color: #ef4444;
    }

    .btn-outline-danger:hover {
        background-color: #ef4444;
        border-color: #ef4444;
        color: white;
    }

    .btn-outline-info {
        border-color: #3b82f6;
        color: #3b82f6;
    }

    .btn-outline-info:hover {
        background-color: #3b82f6;
        border-color: #3b82f6;
        color: white;
    }

    .btn-outline-primary {
        border-color: #3b82f6;
        color: #3b82f6;
    }

    .btn-outline-primary:hover {
        background-color: #3b82f6;
        border-color: #3b82f6;
        color: white;
    }

    .status-badge {
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        min-width: 60px;
    }

    .status-new {
        background: #dcfce7;
        color: #166534;
    }

    .status-pending {
        background: #dbeafe;
        color: #1e40af;
    }

    .status-review {
        background: #fed7aa;
        color: #c2410c;
    }

    .status-approved {
        background: #dcfce7;
        color: #166534;
    }

    .action-buttons {
        display: flex;
        gap: 4px;
    }

    .btn-icon {
        width: 28px;
        height: 28px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        transition: all 0.2s;
    }

    .btn-success {
        background: #dcfce7;
        color: #166534;
    }

    .btn-success:hover {
        background: #bbf7d0;
    }

    .btn-danger {
        background: #fee2e2;
        color: #dc2626;
    }

    .btn-danger:hover {
        background: #fecaca;
    }

    .btn-info {
        background: #dbeafe;
        color: #1d4ed8;
    }

    .btn-info:hover {
        background: #bfdbfe;
    }

    .badge {
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        min-width: 60px;
    }

    .bg-success {
        background: #dcfce7;
        color: #166534;
    }

    .bg-warning {
        background: #fed7aa;
        color: #c2410c;
    }

    .bg-danger {
        background: #fee2e2;
        color: #dc2626;
    }

    .bg-info {
        background: #dbeafe;
        color: #1e40af;
    }

    .text-white {
        color: white;
    }

    .checkbox {
        width: 16px;
        height: 16px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .controls-row {
            flex-direction: column;
            align-items: stretch;
        }

        .btn-group {
            justify-content: flex-start;
            gap: 2px;
            white-space: nowrap;
        }

        .btn-group-sm .btn {
            padding: 3px 4px;
            font-size: 10px;
            min-width: 22px;
            line-height: 1.2;
            height: 26px;
        }

        /* 移动端隐藏按钮图标以节省空间 */
        .btn .fas,
        .btn-group .btn .fas,
        .btn-group-sm .btn .fas {
            display: none !important;
        }

        .comment-input {
            width: 100%;
            min-width: 80px;
            max-width: 150px;
        }

        .data-table {
            font-size: 12px;
        }

        .data-table th,
        .data-table td {
            padding: 8px 6px;
        }

        /* 进一步优化列宽度，减少总宽度 */
        .data-table th[style*="width: 180px"] {
            width: 110px !important;
        }

        .data-table th[style*="width: 160px"] {
            width: 130px !important;
        }

        .data-table th[style*="width: 150px"] {
            width: 100px !important;
        }

        .data-table th[style*="width: 140px"] {
            width: 120px !important;
        }

        .data-table th[style*="width: 130px"] {
            width: 110px !important;
        }

        .data-table th[style*="width: 120px"] {
            width: 100px !important;
        }

        .data-table th[style*="width: 110px"] {
            width: 90px !important;
        }

        .data-table th[style*="width: 100px"] {
            width: 85px !important;
        }

        .data-table th[style*="width: 80px"] {
            width: 65px !important;
        }

        .data-table th[style*="width: 70px"] {
            width: 60px !important;
        }

        .data-table th[style*="width: 60px"] {
            width: 50px !important;
        }

        /* 保护操作列的最小宽度，确保按钮完全显示 */
        .data-table th[style*="width: 220px"],
        .data-table td:last-child {
            min-width: 200px !important;
            width: 200px !important;
        }

        /* 强制列宽控制，防止内容撑开表格 */
        .data-table {
            table-layout: fixed !important;
            width: 1230px !important; /* 设置固定总宽度 */
            max-width: 1230px !important;
        }

        .data-table th,
        .data-table td {
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;
            word-wrap: break-word !important;
            box-sizing: border-box !important;
            padding: 8px 4px !important; /* 减少内边距 */
        }

        /* 具体列宽强制控制 - 总计1200px */
        .data-table th:nth-child(1),
        .data-table td:nth-child(1) {
            width: 40px !important;
            max-width: 40px !important;
            min-width: 40px !important;
        }

        .data-table th:nth-child(2),
        .data-table td:nth-child(2) {
            width: 50px !important;
            max-width: 50px !important;
            min-width: 50px !important;
        }

        .data-table th:nth-child(3),
        .data-table td:nth-child(3) {
            width: 100px !important;
            max-width: 100px !important;
            min-width: 100px !important;
        }

        .data-table th:nth-child(4),
        .data-table td:nth-child(4) {
            width: 80px !important;
            max-width: 80px !important;
            min-width: 80px !important;
        }

        .data-table th:nth-child(5),
        .data-table td:nth-child(5) {
            width: 60px !important;
            max-width: 60px !important;
            min-width: 60px !important;
            overflow: visible !important;
            white-space: normal !important;
        }

        .data-table th:nth-child(6),
        .data-table td:nth-child(6) {
            width: 120px !important;
            max-width: 120px !important;
            min-width: 120px !important;
        }

        .data-table th:nth-child(7),
        .data-table td:nth-child(7) {
            width: 280px !important;
            max-width: 280px !important;
            min-width: 280px !important;
            overflow: visible !important;
            white-space: normal !important;
        }

        /* 确保表格容器不会超出 */
        .table-responsive {
            max-width: 1230px !important;
            overflow-x: hidden !important;
        }
    }

    .status-review {
        background: #fed7aa;
        color: #c2410c;
    }

    .status-approved {
        background: #dcfce7;
        color: #166534;
    }

    .action-buttons {
        display: flex;
        gap: 4px;
    }

    .btn-icon {
        width: 28px;
        height: 28px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        transition: all 0.2s;
    }

    .btn-success {
        background: #dcfce7;
        color: #166534;
    }

    .btn-success:hover {
        background: #bbf7d0;
    }

    .btn-danger {
        background: #fee2e2;
        color: #dc2626;
    }

    .btn-danger:hover {
        background: #fecaca;
    }

    .btn-info {
        background: #dbeafe;
        color: #1d4ed8;
    }

    .btn-info:hover {
        background: #bfdbfe;
    }

    .checkbox {
        width: 16px;
        height: 16px;
    }
    
    .table-name {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #495057;
    }
    
    .column-name {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #0D6EFD;
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    
    .loading-content {
        background: white;
        padding: 30px;
        border-radius: 8px;
        text-align: center;
    }
    
    .alert {
        border: none;
        border-radius: 8px;
    }
    
    .pagination {
        justify-content: center;
        margin-top: 20px;
    }
    
    .pagination .page-link {
        border: none;
        color: #0D6EFD;
        margin: 0 2px;
        border-radius: 6px;
    }
    
    .pagination .page-item.active .page-link {
        background: #0D6EFD;
        border-color: #0D6EFD;
    }
</style>
{% endblock %}

{% block content %}
<div class="supplement-container">
    <!-- 页面标题 -->
    <div class="supplement-header">
        <h1><i class="fas fa-edit"></i> 数据补充</h1>
        <p>处理表中文名缺失和字段中文缺失的数据，完善数据字典信息</p>
    </div>
    
    <!-- 筛选区域 -->
    <div class="filter-section">
        {% csrf_token %}
        <form method="get">
            <div class="controls-row">
                <div class="form-group">
                    <label class="form-label">数据库筛选</label>
                    <select name="database" class="form-select">
                        <option value="">全部数据库</option>
                        {% for db in databases %}
                        <option value="{{ db.name }}" {% if db.name == database_filter %}selected{% endif %}>
                            {{ db.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">搜索</label>
                    <input type="text" name="search" class="form-control"
                           placeholder="搜索表名或字段名" value="{{ search }}" style="width: 200px;">
                </div>
                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                    <button type="button" class="btn btn-save-batch" onclick="saveAllChanges()">
                        <i class="fas fa-download"></i> 批量保存
                    </button>
                    {% if tab == 'columns' %}
                    <button type="button" class="btn" onclick="batchTranslate()">
                        <i class="fas fa-language"></i> 批量翻译
                    </button>
                    <button type="button" class="btn" onclick="batchConfirmAI()">
                        <i class="fas fa-check-circle"></i> 批量采纳
                    </button>
                    <button type="button" class="btn" onclick="batchRejectAI()">
                        <i class="fas fa-times-circle"></i> 批量拒绝
                    </button>
                    <button type="button" class="btn" onclick="selectAllData()" title="选择全部符合筛选条件的字段">
                        <i class="fas fa-check-square"></i> 选择全部
                    </button>
                    {% endif %}
                </div>
            </div>
            <input type="hidden" name="tab" value="{{ tab }}">
        </form>

        {% if tab == 'columns' %}
        <div class="confidence-legend">
            <span style="font-weight: 500;">AI建议置信度：</span>
            <div class="legend-item">
                <div class="color-box confidence-high-color"></div>
                <span>高置信度 (≥0.8)</span>
            </div>
            <div class="legend-item">
                <div class="color-box confidence-medium-color"></div>
                <span>中等置信度 (0.6-0.8)</span>
            </div>
            <div class="legend-item">
                <div class="color-box confidence-low-color"></div>
                <span>低置信度 (<0.6)</span>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 数据区域 -->
    <div class="data-section">
        <div class="section-header">
            <div class="section-tabs">
                <button class="tab {% if tab == 'tables' %}active{% endif %}" onclick="location.href='?tab=tables&database={{ database_filter|urlencode }}&search={{ search|urlencode }}'">
                    <i class="fas fa-table"></i>
                    表中文名缺失
                    {% if tab == 'tables' %}<span class="stats-badge">{{ tables_count }}条</span>{% endif %}
                </button>
                <button class="tab {% if tab == 'columns' %}active{% endif %}" onclick="location.href='?tab=columns&database={{ database_filter|urlencode }}&search={{ search|urlencode }}'">
                    <i class="fas fa-columns"></i>
                    字段中文名缺失
                    {% if tab == 'columns' %}<span class="stats-badge">{{ columns_count }}条</span>{% endif %}
                </button>
            </div>
        </div>



    <ul class="nav nav-tabs" role="tablist" style="display: none;">
        <!-- 隐藏的导航，保持原有结构 -->
    </ul>
    </ul>
    
    <!-- 数据表格 -->
    <div class="data-table">
        {% if tab == 'tables' %}
        <!-- 表中文名缺失 -->
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th style="width: 40px;">序号</th>
                        <th style="width: 70px;">数据库</th>
                        <th style="width: 140px;">表名</th>
                        <th style="max-width: 120px;">中文名</th>
                        <th style="width: 200px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for table in tables %}
                    <tr data-table-id="{{ table.id }}">
                        <td>{{ forloop.counter0|add:tables.start_index }}</td>
                        <td><span class="database-badge">{{ table.database_name }}</span></td>
                        <td><span class="table-name">{{ table.name }}</span></td>
                        <td style="width: auto;">
                            <div style="display: flex; align-items: center;">
                                <input type="text" class="comment-input"
                                       data-original="{{ table.comment|default:'' }}"
                                       value="{{ table.comment|default:'' }}"
                                       placeholder="请输入表的中文名">
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-outline-primary" onclick="saveTableComment({{ table.id }})" title="保存">
                                <i class="fas fa-save"></i> 保存
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-2x mb-2"></i><br>
                            没有找到表中文名缺失的数据
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if tables.has_other_pages %}
        <div class="p-3">
            <nav>
                <ul class="pagination">
                    {% if tables.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?tab=tables&page={{ tables.previous_page_number }}&database={{ database_filter|urlencode }}&search={{ search|urlencode }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in tables.paginator.page_range %}
                    {% if num == tables.number %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% else %}
                    <li class="page-item">
                        <a class="page-link" href="?tab=tables&page={{ num }}&database={{ database_filter|urlencode }}&search={{ search|urlencode }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if tables.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?tab=tables&page={{ tables.next_page_number }}&database={{ database_filter|urlencode }}&search={{ search|urlencode }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <!-- 字段中文名缺失 -->
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th style="width: 60px;">数据库</th>
                        <th style="width: 120px;">表名</th>
                        <th style="width: 100px;">字段名</th>
                        <th style="max-width: 90px;">中文名</th>
                        <th style="width: 130px;">AI建议</th>
                        <th style="width: 220px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for column in columns %}
                    <tr data-column-id="{{ column.id }}">
                        <td>
                            <input type="checkbox" class="column-checkbox" value="{{ column.id }}" onchange="handleCheckboxChange()">
                        </td>
                        <td><span class="database-badge">{{ column.database_name }}</span></td>
                        <td><span class="table-name">{{ column.table_name }}</span></td>
                        <td><span class="column-name">{{ column.name }}</span></td>
                        <td style="width: auto;">
                            <div style="display: flex; align-items: center;">
                                <input type="text" class="comment-input"
                                       data-original="{{ column.comment|default:'' }}"
                                       value="{{ column.comment|default:'' }}"
                                       placeholder="请输入字段的中文名">
                            </div>
                        </td>
                        <td>
                            <div id="ai-suggestion-{{ column.id }}">
                                {% if column.ai_suggested_name %}
                                    {% if column.ai_confidence_score >= 0.8 %}
                                        <span class="badge confidence-high-color text-white">{{ column.ai_suggested_name }}</span>
                                    {% elif column.ai_confidence_score >= 0.6 %}
                                        <span class="badge confidence-medium-color text-dark">{{ column.ai_suggested_name }}</span>
                                    {% else %}
                                        <span class="badge confidence-low-color text-white">{{ column.ai_suggested_name }}</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">暂无建议</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group" aria-label="字段操作">
                                {% if column.ai_suggested_name %}
                                    <!-- 有AI建议时的按钮组合 -->
                                    <button class="btn btn-outline-primary" onclick="confirmAiTranslation({{ column.id }}, 'confirm')" title="保存AI建议">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="confirmAiTranslation({{ column.id }}, 'reject')" title="拒绝AI建议">
                                        <i class="fas fa-times"></i> 拒绝
                                    </button>
                                    <button class="btn btn-outline-success" onclick="retranslateField({{ column.id }})" title="重新翻译">
                                        <i class="fas fa-redo"></i> 重译
                                    </button>
                                {% else %}
                                    <!-- 无AI建议时的按钮组合 -->
                                    <button class="btn btn-outline-success" onclick="aiTranslateField({{ column.id }})" title="AI翻译">
                                        <i class="fas fa-robot"></i> AI翻译
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="saveColumnComment({{ column.id }})" title="手动保存">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-2x mb-2"></i><br>
                            没有找到字段中文名缺失的数据
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if columns.has_other_pages %}
        <div class="p-3">
            <nav>
                <ul class="pagination">
                    {% if columns.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?tab=columns&page={{ columns.previous_page_number }}&database={{ database_filter|urlencode }}&search={{ search|urlencode }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in columns.paginator.page_range %}
                    {% if num == columns.number %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% else %}
                    <li class="page-item">
                        <a class="page-link" href="?tab=columns&page={{ num }}&database={{ database_filter|urlencode }}&search={{ search|urlencode }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if columns.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?tab=columns&page={{ columns.next_page_number }}&database={{ database_filter|urlencode }}&search={{ search|urlencode }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        {% endif %}
    </div>
</div>

<!-- 加载遮罩 -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div class="loading-message">正在保存数据...</div>
    </div>
</div>

<!-- 消息提示区域 -->
<div id="messageContainer" style="position: fixed; top: 80px; right: 20px; z-index: 1050;"></div>
{% endblock %}

{% block extra_js %}
<script>
// 显示消息提示
function showMessage(message, type = 'success') {
    const container = document.getElementById('messageContainer');
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas ${iconClass} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    container.appendChild(alertDiv);

    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// 显示/隐藏加载遮罩
function showLoading(message = '正在保存数据...') {
    const overlay = document.getElementById('loadingOverlay');
    const messageElement = overlay.querySelector('.loading-message');
    if (messageElement) {
        messageElement.textContent = message;
    }
    overlay.style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// 显示翻译加载状态
function showTranslatingLoading(current = 0, total = 0) {
    let message = '正在翻译...';
    if (total > 0) {
        message = `正在翻译... (${current}/${total})`;
    }

    // 创建带停止按钮的加载界面
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.innerHTML = `
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-3">${message}</div>
            <button class="btn btn-danger mt-3" onclick="stopTranslation()">
                <i class="fas fa-stop"></i> 停止翻译
            </button>
        </div>
    `;
    loadingOverlay.style.display = 'flex';
}

// 保存单个表注释
function saveTableComment(tableId) {
    const row = document.querySelector(`tr[data-table-id="${tableId}"]`);
    const input = row.querySelector('.comment-input');
    const comment = input.value.trim();

    showLoading();

    fetch('{% url "data_dictionary:update_table_comments" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            updates: [{
                id: tableId,
                comment: comment
            }]
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showMessage('表注释保存成功');
            input.setAttribute('data-original', comment);
            // 如果注释不为空，可以考虑从列表中移除该行
            if (comment) {
                row.style.opacity = '0.5';
                setTimeout(() => {
                    row.remove();
                }, 1000);
            }
        } else {
            showMessage(data.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('保存表注释时发生错误:', error);

        let errorMsg = '网络错误，请重试';
        if (error.name === 'TypeError') {
            errorMsg = '网络连接失败，请检查网络连接';
        } else if (error.message) {
            errorMsg = '保存失败: ' + error.message;
        }

        showMessage(errorMsg, 'error');
    });
}

// 保存单个字段注释
function saveColumnComment(columnId) {
    const row = document.querySelector(`tr[data-column-id="${columnId}"]`);
    const input = row.querySelector('.comment-input');
    const comment = input.value.trim();

    // 检查是否有AI建议
    const suggestionDiv = document.getElementById(`ai-suggestion-${columnId}`);
    const hasAiSuggestion = suggestionDiv.querySelector('.badge') !== null;

    // 验证：如果中文名为空且没有AI建议，则不允许保存
    if (!comment && !hasAiSuggestion) {
        showMessage('请输入中文名称或使用AI翻译功能', 'error');
        return;
    }

    showLoading();

    fetch('{% url "data_dictionary:update_column_comments" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            updates: [{
                id: columnId,
                comment: comment
            }]
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showMessage('字段注释保存成功');
            input.setAttribute('data-original', comment);
            // 如果注释不为空，可以考虑从列表中移除该行
            if (comment) {
                row.style.opacity = '0.5';
                setTimeout(() => {
                    row.remove();
                }, 1000);
            }
        } else {
            showMessage(data.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showMessage('网络错误，请重试', 'error');
        console.error('Error:', error);
    });
}

// 批量保存所有修改
function saveAllChanges() {
    const currentTab = '{{ tab }}';
    let updates = [];
    let hasChanges = false;

    if (currentTab === 'tables') {
        // 收集表注释的修改
        document.querySelectorAll('tr[data-table-id]').forEach(row => {
            const input = row.querySelector('.comment-input');
            const original = input.getAttribute('data-original') || '';
            const current = input.value.trim();

            if (original !== current) {
                hasChanges = true;
                updates.push({
                    id: parseInt(row.getAttribute('data-table-id')),
                    comment: current
                });
            }
        });

        if (!hasChanges) {
            showMessage('没有需要保存的修改', 'error');
            return;
        }

        showLoading();

        fetch('{% url "data_dictionary:update_table_comments" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({ updates: updates })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showMessage(`成功保存 ${updates.length} 条表注释`);
                // 更新原始值并移除已填写注释的行
                updates.forEach(update => {
                    const row = document.querySelector(`tr[data-table-id="${update.id}"]`);
                    if (row && update.comment) {
                        row.style.opacity = '0.5';
                        setTimeout(() => {
                            row.remove();
                        }, 1000);
                    }
                });
            } else {
                showMessage(data.message || '保存失败', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showMessage('网络错误，请重试', 'error');
            console.error('Error:', error);
        });

    } else {
        // 收集字段注释的修改
        document.querySelectorAll('tr[data-column-id]').forEach(row => {
            const input = row.querySelector('.comment-input');
            const original = input.getAttribute('data-original') || '';
            const current = input.value.trim();

            if (original !== current) {
                hasChanges = true;
                updates.push({
                    id: parseInt(row.getAttribute('data-column-id')),
                    comment: current
                });
            }
        });

        if (!hasChanges) {
            showMessage('没有需要保存的修改', 'error');
            return;
        }

        showLoading();

        fetch('{% url "data_dictionary:update_column_comments" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({ updates: updates })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showMessage(`成功保存 ${updates.length} 条字段注释`);
                // 更新原始值并移除已填写注释的行
                updates.forEach(update => {
                    const row = document.querySelector(`tr[data-column-id="${update.id}"]`);
                    if (row && update.comment) {
                        row.style.opacity = '0.5';
                        setTimeout(() => {
                            row.remove();
                        }, 1000);
                    }
                });
            } else {
                showMessage(data.message || '保存失败', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showMessage('网络错误，请重试', 'error');
            console.error('Error:', error);
        });
    }
}

// 回车键保存
document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && e.target.classList.contains('comment-input')) {
        e.preventDefault();
        const row = e.target.closest('tr');
        if (row.hasAttribute('data-table-id')) {
            saveTableComment(parseInt(row.getAttribute('data-table-id')));
        } else if (row.hasAttribute('data-column-id')) {
            saveColumnComment(parseInt(row.getAttribute('data-column-id')));
        }
    }
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 为输入框添加变化提示
    document.querySelectorAll('.comment-input').forEach(input => {
        input.addEventListener('input', function() {
            const original = this.getAttribute('data-original') || '';
            const current = this.value.trim();

            if (original !== current) {
                this.style.borderColor = '#28a745';
                this.style.backgroundColor = '#f8fff9';
            } else {
                this.style.borderColor = '#ced4da';
                this.style.backgroundColor = 'white';
            }
        });
    });
});

// AI翻译字段
function aiTranslateField(columnId, callback = null) {
    const row = document.querySelector(`tr[data-column-id="${columnId}"]`);
    const actionCell = row.querySelector('td:last-child .btn-group');
    const translateButton = actionCell.querySelector('button[onclick*="aiTranslateField"]');
    const retranslateButton = actionCell.querySelector('button[onclick*="retranslateField"]');

    // 确定当前是翻译还是重译操作
    const isRetranslate = retranslateButton && retranslateButton.disabled;
    const currentButton = isRetranslate ? retranslateButton : translateButton;

    if (currentButton) {
        const originalText = currentButton.innerHTML;
        // 显示加载状态
        if (isRetranslate) {
            currentButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 重译中...';
        } else {
            currentButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 翻译中...';
        }
        currentButton.disabled = true;
    }

    fetch('{% url "data_dictionary:ai_translate_field" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            column_id: columnId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新AI建议显示
            const suggestionDiv = document.getElementById(`ai-suggestion-${columnId}`);
            let badgeClass = '';
            let textClass = '';
            if (data.confidence >= 0.8) {
                badgeClass = 'confidence-high-color';
                textClass = 'text-white';
            } else if (data.confidence >= 0.6) {
                badgeClass = 'confidence-medium-color';
                textClass = 'text-dark';
            } else {
                badgeClass = 'confidence-low-color';
                textClass = 'text-white';
            }
            suggestionDiv.innerHTML = `
                <span class="badge ${badgeClass} ${textClass}">${data.chinese_name}</span>
            `;

            // 更新操作列按钮组合
            actionCell.innerHTML = `
                <button class="btn btn-outline-primary" onclick="confirmAiTranslation(${columnId}, 'confirm')" title="保存AI建议">
                    <i class="fas fa-save"></i> 保存
                </button>
                <button class="btn btn-outline-danger" onclick="confirmAiTranslation(${columnId}, 'reject')" title="拒绝AI建议">
                    <i class="fas fa-times"></i> 拒绝
                </button>
                <button class="btn btn-outline-success" onclick="retranslateField(${columnId})" title="重新翻译">
                    <i class="fas fa-redo"></i> 重译
                </button>
            `;

            showMessage(isRetranslate ? '重译成功' : 'AI翻译成功');
            if (callback) callback();
        } else {
            // 翻译失败时，显示失败状态而不是恢复原始按钮
            const suggestionDiv = document.getElementById(`ai-suggestion-${columnId}`);
            suggestionDiv.innerHTML = `
                <span class="badge bg-danger text-white">翻译失败</span>
            `;

            // 更新按钮组合，显示重试选项
            actionCell.innerHTML = `
                <button class="btn btn-outline-primary" onclick="aiTranslateField(${columnId})" title="重试翻译">
                    <i class="fas fa-redo"></i> 重试
                </button>
            `;

            showMessage(data.message || (isRetranslate ? '重译失败' : 'AI翻译失败'), 'error');
            if (callback) callback();
        }
    })
    .catch(error => {
        // 网络错误时，也显示失败状态
        const suggestionDiv = document.getElementById(`ai-suggestion-${columnId}`);
        suggestionDiv.innerHTML = `
            <span class="badge bg-warning text-dark">网络错误</span>
        `;

        // 更新按钮组合，显示重试选项
        actionCell.innerHTML = `
            <button class="btn btn-outline-primary" onclick="aiTranslateField(${columnId})" title="重试翻译">
                <i class="fas fa-redo"></i> 重试
            </button>
        `;

        showMessage('网络错误，请重试', 'error');
        console.error('Error:', error);
        if (callback) callback();
    });
}

// 确认AI翻译
function confirmAiTranslation(columnId, action, callback = null) {
    const row = document.querySelector(`tr[data-column-id="${columnId}"]`);
    const input = row.querySelector('.comment-input');
    const suggestionDiv = document.getElementById(`ai-suggestion-${columnId}`);
    const aiSuggestion = suggestionDiv.querySelector('.badge').textContent;

    let chineseName = '';
    if (action === 'confirm') {
        chineseName = input.value.trim() || aiSuggestion;
    }

    showLoading();

    fetch('{% url "data_dictionary:confirm_ai_translation" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            column_id: columnId,
            action: action,
            chinese_name: chineseName
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showMessage(data.message);
            if (action === 'confirm') {
                // 更新输入框值
                input.value = chineseName;
                input.setAttribute('data-original', chineseName);
                // 淡出并移除行
                row.style.opacity = '0.5';
                setTimeout(() => {
                    row.remove();
                }, 1000);
            } else {
                // 拒绝：重置AI建议区域
                suggestionDiv.innerHTML = '<span class="text-muted">暂无建议</span>';

                // 重置操作按钮，恢复到无AI建议状态
                const actionCell = row.querySelector('td:last-child .btn-group');
                actionCell.innerHTML = `
                    <button class="btn btn-outline-success" onclick="aiTranslateField(${columnId})" title="AI翻译">
                        <i class="fas fa-robot"></i> AI翻译
                    </button>
                    <button class="btn btn-outline-primary" onclick="saveColumnComment(${columnId})" title="手动保存">
                        <i class="fas fa-save"></i> 保存
                    </button>
                `;
            }
            if (callback) callback();
        } else {
            showMessage(data.message || '操作失败', 'error');
            if (callback) callback();
        }
    })
    .catch(error => {
        hideLoading();
        showMessage('网络错误，请重试', 'error');
        console.error('Error:', error);
        if (callback) callback();
    });
}

// 重新翻译字段
function retranslateField(columnId) {
    const row = document.querySelector(`tr[data-column-id="${columnId}"]`);
    const actionCell = row.querySelector('td:last-child .btn-group');
    const retranslateButton = actionCell.querySelector('button[onclick*="retranslateField"]');

    if (retranslateButton) {
        // 显示重译加载状态
        retranslateButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 重译中...';
        retranslateButton.disabled = true;
    }

    // 调用AI翻译功能
    aiTranslateField(columnId);
}

// 全局选中的字段ID数组
let globalSelectedIds = [];

// 翻译状态管理
let translationState = {
    isTranslating: false,
    abortController: null,
    currentBatch: [],
    completedCount: 0,
    totalCount: 0
};

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');

    if (selectAll.checked) {
        // 选中当前页面的所有复选框
        const checkboxes = document.querySelectorAll('.column-checkbox');
        const currentPageIds = [];

        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
            currentPageIds.push(parseInt(checkbox.value));
        });

        // 清空全局选择，只保留当前页面的选择
        globalSelectedIds = [];

        // 显示当前页面选中数量
        const currentPageCount = currentPageIds.length;
        showMessage(`已选中当前页面 ${currentPageCount} 个字段`);

        // 延迟显示选择全部的选项
        setTimeout(() => {
            if (confirm(`当前已选中本页 ${currentPageCount} 个字段。\n\n是否要选择全部符合筛选条件的字段？`)) {
                selectAllData();
            }
        }, 500);

    } else {
        // 取消全选
        globalSelectedIds = [];
        const checkboxes = document.querySelectorAll('.column-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        showMessage('已取消全选');
    }
}

// 选择全部数据的函数
function selectAllData() {
    showLoading('正在获取全部字段列表...');

    const urlParams = new URLSearchParams(window.location.search);
    const database = urlParams.get('database') || '';
    const search = urlParams.get('search') || '';

    fetch(`{% url "data_dictionary:get_all_column_ids" %}?database=${database}&search=${search}`)
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            globalSelectedIds = data.column_ids;
            // 选中当前页面的所有复选框
            const checkboxes = document.querySelectorAll('.column-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = globalSelectedIds.includes(parseInt(checkbox.value));
            });
            showMessage(`已选择全部 ${data.total_count} 个字段`);
        } else {
            showMessage(data.message || '获取字段列表失败', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showMessage('网络错误，请重试', 'error');
        console.error('Error:', error);
    });
}

// 获取选中的字段ID
function getSelectedColumnIds() {
    if (globalSelectedIds.length > 0) {
        return globalSelectedIds;
    }

    // 如果没有全局选择，则返回当前页面选中的
    const checkboxes = document.querySelectorAll('.column-checkbox:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.value));
}

// 获取选中数量的显示文本
function getSelectedCountText() {
    const currentPageChecked = document.querySelectorAll('.column-checkbox:checked');

    if (globalSelectedIds.length > 0) {
        return `已选择全部 ${globalSelectedIds.length} 个字段`;
    } else if (currentPageChecked.length > 0) {
        return `已选择当前页 ${currentPageChecked.length} 个字段`;
    } else {
        return '未选择任何字段';
    }
}

// 单个复选框变化时的处理
function handleCheckboxChange() {
    const checkboxes = document.querySelectorAll('.column-checkbox');
    const checkedBoxes = document.querySelectorAll('.column-checkbox:checked');
    const selectAll = document.getElementById('selectAll');

    // 如果是全选状态，单个取消选择时需要更新全局选择
    if (globalSelectedIds.length > 0) {
        const currentPageIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
        const currentPageCheckedIds = Array.from(checkedBoxes).map(cb => parseInt(cb.value));

        // 从全局选择中移除当前页面未选中的
        globalSelectedIds = globalSelectedIds.filter(id =>
            !currentPageIds.includes(id) || currentPageCheckedIds.includes(id)
        );

        // 添加当前页面新选中的
        currentPageCheckedIds.forEach(id => {
            if (!globalSelectedIds.includes(id)) {
                globalSelectedIds.push(id);
            }
        });

        // 如果全局选择为空，取消全选状态
        if (globalSelectedIds.length === 0) {
            selectAll.checked = false;
        }
    }

    // 更新全选复选框状态
    selectAll.checked = checkedBoxes.length === checkboxes.length && checkboxes.length > 0;
}

// 批量翻译
function batchTranslate() {
    // 检查是否正在翻译
    if (translationState.isTranslating) {
        showMessage('正在翻译中，请等待完成或点击停止按钮', 'warning');
        return;
    }

    const selectedIds = getSelectedColumnIds();
    if (selectedIds.length === 0) {
        showMessage('请先选择要翻译的字段', 'error');
        return;
    }

    // 简化确认流程，只需要一次确认
    const countText = getSelectedCountText();
    if (!confirm(`${countText}\n\n确定要进行批量翻译吗？`)) {
        return;
    }

    // 初始化翻译状态
    translationState.isTranslating = true;
    translationState.abortController = new AbortController();
    translationState.currentBatch = selectedIds;
    translationState.completedCount = 0;
    translationState.totalCount = selectedIds.length;

    showTranslatingLoading(0, selectedIds.length);

    fetch('{% url "data_dictionary:ai_translate_fields_batch" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            column_ids: selectedIds
        }),
        signal: translationState.abortController.signal
    })
    .then(response => response.json())
    .then(data => {
        // 重置翻译状态
        resetTranslationState();
        hideLoading();

        if (data.success) {
            showMessage(data.message);

            // 更新页面显示
            data.results.forEach(result => {
                if (result.success) {
                    const suggestionDiv = document.getElementById(`ai-suggestion-${result.column_id}`);
                    if (suggestionDiv) {
                        let badgeClass = '';
                        let textClass = '';
                        if (result.confidence >= 0.8) {
                            badgeClass = 'confidence-high-color';
                            textClass = 'text-white';
                        } else if (result.confidence >= 0.6) {
                            badgeClass = 'confidence-medium-color';
                            textClass = 'text-dark';
                        } else {
                            badgeClass = 'confidence-low-color';
                            textClass = 'text-white';
                        }

                        // 只在AI建议列显示翻译结果
                        suggestionDiv.innerHTML = `
                            <span class="badge ${badgeClass} ${textClass}">${result.chinese_name}</span>
                        `;

                        // 更新操作列的按钮组合
                        const row = document.querySelector(`tr[data-column-id="${result.column_id}"]`);
                        if (row) {
                            const actionCell = row.querySelector('td:last-child .btn-group');
                            if (actionCell) {
                                actionCell.innerHTML = `
                                    <button class="btn btn-outline-primary" onclick="confirmAiTranslation(${result.column_id}, 'confirm')" title="保存AI建议">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="confirmAiTranslation(${result.column_id}, 'reject')" title="拒绝AI建议">
                                        <i class="fas fa-times"></i> 拒绝
                                    </button>
                                    <button class="btn btn-outline-success" onclick="retranslateField(${result.column_id})" title="重新翻译">
                                        <i class="fas fa-redo"></i> 重译
                                    </button>
                                `;
                            }
                        }
                    }
                } else {
                    // 翻译失败时的处理
                    const suggestionDiv = document.getElementById(`ai-suggestion-${result.column_id}`);
                    if (suggestionDiv) {
                        // AI建议列只显示失败状态，不包含按钮
                        suggestionDiv.innerHTML = `
                            <span class="badge bg-danger text-white">翻译失败</span>
                        `;
                    }

                    // 更新操作列的按钮组合 - 显示重试和保存按钮
                    const row = document.querySelector(`tr[data-column-id="${result.column_id}"]`);
                    if (row) {
                        const actionCell = row.querySelector('td:last-child .btn-group');
                        if (actionCell) {
                            actionCell.innerHTML = `
                                <button class="btn btn-outline-success" onclick="aiTranslateField(${result.column_id})" title="重试翻译">
                                    <i class="fas fa-redo"></i> 重试
                                </button>
                                <button class="btn btn-outline-primary" onclick="saveFieldComment(${result.column_id})" title="保存当前输入">
                                    <i class="fas fa-save"></i> 保存
                                </button>
                            `;
                        }
                    }
                }
            });
        } else {
            showMessage(data.message || '批量翻译失败', 'error');
        }
    })
    .catch(error => {
        // 重置翻译状态
        resetTranslationState();
        hideLoading();

        if (error.name === 'AbortError') {
            showMessage('翻译已停止', 'info');
        } else {
            showMessage('网络错误，请重试', 'error');
            console.error('Error:', error);
        }
    });
}

// 重置翻译状态
function resetTranslationState() {
    translationState.isTranslating = false;
    translationState.abortController = null;
    translationState.currentBatch = [];
    translationState.completedCount = 0;
    translationState.totalCount = 0;
}

// 停止翻译
function stopTranslation() {
    if (translationState.isTranslating && translationState.abortController) {
        translationState.abortController.abort();
        resetTranslationState();
        hideLoading();
        showMessage('翻译已停止', 'info');
    }
}

// 重试单个字段翻译
function retryTranslation(columnId) {
    const row = document.querySelector(`tr[data-column-id="${columnId}"]`);
    if (!row) return;

    const fieldName = row.querySelector('td:nth-child(2)').textContent.trim();
    const dataType = row.querySelector('td:nth-child(3)').textContent.trim();
    const tableName = row.querySelector('td:nth-child(1)').textContent.trim();

    // 调用单个字段翻译
    aiTranslateField(columnId);
}

// 批量采纳AI建议
function batchConfirmAI() {
    const selectedIds = getSelectedColumnIds();
    if (selectedIds.length === 0) {
        showMessage('请先选择要采纳AI建议的字段', 'error');
        return;
    }

    // 简化确认流程
    const countText = getSelectedCountText();
    if (!confirm(`${countText}\n\n确定要批量采纳AI建议吗？`)) {
        return;
    }

    // 过滤出有AI建议的字段
    const fieldsWithAI = selectedIds.filter(id => {
        const suggestionDiv = document.getElementById(`ai-suggestion-${id}`);
        return suggestionDiv.querySelector('.badge') !== null;
    });

    if (fieldsWithAI.length === 0) {
        showMessage('选中的字段中没有AI建议可以采纳', 'error');
        return;
    }

    if (!confirm(`确定要采纳选中的 ${fieldsWithAI.length} 个字段的AI建议吗？`)) {
        return;
    }

    showLoading();
    let completed = 0;
    let total = fieldsWithAI.length;

    fieldsWithAI.forEach(columnId => {
        confirmAiTranslation(columnId, 'confirm', () => {
            completed++;
            if (completed === total) {
                hideLoading();
                showMessage(`批量采纳完成，共处理 ${total} 个字段`);
            }
        });
    });
}

// 批量拒绝AI建议
function batchRejectAI() {
    const selectedIds = getSelectedColumnIds();
    if (selectedIds.length === 0) {
        showMessage('请先选择要拒绝AI建议的字段', 'error');
        return;
    }

    // 简化确认流程
    const countText = getSelectedCountText();
    if (!confirm(`${countText}\n\n确定要批量拒绝AI建议吗？`)) {
        return;
    }

    // 过滤出有AI建议的字段
    const fieldsWithAI = selectedIds.filter(id => {
        const suggestionDiv = document.getElementById(`ai-suggestion-${id}`);
        return suggestionDiv.querySelector('.badge') !== null;
    });

    if (fieldsWithAI.length === 0) {
        showMessage('选中的字段中没有AI建议可以拒绝', 'error');
        return;
    }

    if (!confirm(`确定要拒绝选中的 ${fieldsWithAI.length} 个字段的AI建议吗？`)) {
        return;
    }

    showLoading();
    let completed = 0;
    let total = fieldsWithAI.length;

    fieldsWithAI.forEach(columnId => {
        confirmAiTranslation(columnId, 'reject', () => {
            completed++;
            if (completed === total) {
                hideLoading();
                showMessage(`批量拒绝完成，共处理 ${total} 个字段`);
            }
        });
    });
}
</script>
{% endblock %}
