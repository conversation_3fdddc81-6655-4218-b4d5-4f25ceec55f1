import datetime
import json
import warnings
from pathlib import Path
import jwt
import numpy as np
import pandas as pd
import streamlit as st
from openpyxl.styles import PatternFill
from openpyxl import Workbook, load_workbook
from urllib.parse import quote
import streamlit_antd_components as sac
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from pyecharts import options as opts
from pyecharts.charts import Pie
from streamlit_echarts import st_pyecharts
from pandasql import sqldf
from st_aggrid import JsCode, AgGrid, GridOptionsBuilder, GridUpdateMode
from utils.auth import auth_from_session, get_agent_auth, JWT_SECRET, agent_auth_check
from utils.utils import send_feishu_message
from utils.st import query_sql, text_write, empty_line, sub_text_write,text_new
from utils.date import get_shifted_dates

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)

CONNECTOR = st.connection('claim', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品代码、金额等信息
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT 
        GROUP_CONCAT( p.CODE SEPARATOR ',' ) product_code,
        valid_from,
        pss.CODE product_serial_code,
        pss.`name` product_serial_name
    FROM
        product p
        JOIN product_set ps ON p.product_set_code = ps.
        CODE JOIN product_serial pss ON ps.product_serial_code = pss.CODE
    where pss.code in ('hunan_amb','jiangsu_yhb','jiujiang_hxb','neimenggu_hmb','shanxi_jkb','xiantao_xhb','yichun_hmb') 
    GROUP BY
        pss.CODE 
    ORDER BY
        pss.CODE,
        ps.CODE DESC,
        valid_from DESC
        '''

    df_product_code = CONNECTOR.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=10)
    df_product_code['product_code_list'] = df_product_code['product_code'].apply(lambda x: x.split(','))
    # 将product_code中的字符串用单引号包裹
    df_product_code['product_code'] = df_product_code['product_code_list'].apply(
        lambda x: ','.join([f"'{i}'" for i in x]))
    df_product_code.drop(columns=['product_code_list'], inplace=True)
    return df_product_code


def safe_json_load(s):
    try:
        return json.loads(s)
    except (json.JSONDecodeError, TypeError):
        return {}


def aggregate_data(df, freq):
    """
    根据传入的参数（日或周）对DataFrame进行统计
    :param df: 包含日期、编号、身份证号码的DataFrame
    :param freq: 统计频率，'日' 或 '周'
    :return: 统计后的DataFrame
    """
    # 确保日期列是datetime类型
    df['日期'] = pd.to_datetime(df['日期'])
    product_list = df['产品系列'].unique()
    df_total = pd.DataFrame()
    for product in product_list:
        # 按日或周进行统计
        if freq == '日':
            # 按日期分组，统计去重的编号和身份证号码数量
            result = df[df['产品系列'] == product].groupby('日期').agg(
                项目=('项目', 'first'),
                产品系列=('产品系列', 'first'),
                总数=('总数', 'sum'),
                OCR引用数=('OCR引用数', 'sum')
            ).reset_index()
        elif freq == '周':
            # 按周分组，统计去重的编号和身份证号码数量
            result = df[df['产品系列'] == product].resample('W', on='日期').agg(
                项目=('项目', 'first'),
                产品系列=('产品系列', 'first'),
                总数=('总数', 'sum'),
                OCR引用数=('OCR引用数', 'sum')
            ).reset_index()
        elif freq == '月':
            # 按月分组，统计去重的编号和身份证号码数量
            result = df[df['产品系列'] == product].resample('M', on='日期').agg(
                项目=('项目', 'first'),
                产品系列=('产品系列', 'first'),
                总数=('总数', 'sum'),
                OCR引用数=('OCR引用数', 'sum')
            ).reset_index()

        else:
            raise ValueError("freq参数必须是'日'、'周'、'月'中的一个")
        result['产品系列'] = product
        if '就诊' in result['项目'].unique():
            result['项目'] = '就诊'
        else:
            result['项目'] = '发票'
        df_total = pd.concat([df_total, result], axis=0)

    return df_total


def aggregate_data_v2(df, freq):
    """
    根据传入的参数（日或周或月）对DataFrame进行统计
    :param df: 包含日期、编号、身份证号码的DataFrame
    :param freq: 统计频率，'日' 或 '周' 或 '月'
    :return: 统计后的DataFrame，包含产品系列、日期、字段、正确数量、总数量、准确率
    """
    # 确保日期列是datetime类型
    df['日期'] = pd.to_datetime(df['日期'])

    # 定义频率参数
    freq_mapping = {
        '日': 'D',
        '周': 'W',
        '月': 'M'
    }

    if freq not in freq_mapping:
        raise ValueError("freq参数必须是'日'、'周'、'月'中的一个")

    # 按照产品系列、字段和频率对日期进行分组
    result = df.groupby(['产品系列', '字段', pd.Grouper(key='日期', freq=freq_mapping[freq])]).agg(
        正确数量=('正确数量', 'sum'),
        总数量=('总数量', 'sum')
    ).reset_index()

    # 计算准确率
    result['准确率'] = result.apply(lambda x: round(x['正确数量'] / x['总数量'] * 100, 2) if x['总数量'] > 0 else 0,
                                    axis=1)

    return result


def aggregate_data_v3(df, freq):
    """
    根据传入的参数（日或周或月）对DataFrame进行统计
    :param df: 包含日期、编号、身份证号码的DataFrame
    :param freq: 统计频率，'日' 或 '周' 或 '月'
    :return: 统计后的DataFrame，包含产品系列、日期、字段、正确数量、总数量、准确率
    """
    # 确保日期列是datetime类型
    df['日期'] = pd.to_datetime(df['日期'])
    df['正确数量'] = df['正确数量'].apply(lambda x: np.nan if x == '未识别' else x)
    df['正确数量'] = pd.to_numeric(df['正确数量'], errors='coerce')
    df['正确数量V1'] = pd.to_numeric(df['正确数量V1'], errors='coerce')
    df['总数量'] = pd.to_numeric(df['总数量'], errors='coerce')
    df['总数量V1'] = pd.to_numeric(df['总数量V1'], errors='coerce')

    # 定义频率参数
    freq_mapping = {
        '日': 'D',
        '周': 'W',
        '月': 'M'
    }

    if freq not in freq_mapping:
        raise ValueError("freq参数必须是'日'、'周'、'月'中的一个")

    # 按照产品系列、字段和频率对日期进行分组
    result = df.groupby(['产品系列', '字段', pd.Grouper(key='日期', freq=freq_mapping[freq])]).agg(
        正确数量=('正确数量', 'sum'),
        总数量=('总数量', 'sum'),
        总数量V1=('总数量V1', 'sum'),
        正确数量V1=('正确数量V1', 'sum'),
    ).reset_index()

    # 计算准确率
    result['准确率'] = result.apply(
        lambda x: round(x['正确数量'] / x['总数量'] * 100, 2) if x['总数量'] > 0 else np.nan,axis=1)
    result['准确率V1'] = result.apply(
        lambda x: round(x['正确数量V1'] / x['总数量V1'] * 100, 2) if x['总数量V1'] > 0 else np.nan,axis=1)

    return result


def get_ocr_medical(product_serial_code, start_time=None, end_time=None, sql=query_sql('SQL_OCR_MEDICAL')):
    """
    获取就诊ocr引用情况
    :param product_code: 产品code
    :param start_date: 数据开始日期
    :param end_date: 数据结束日期
    :param age_start: 年龄起始
    :param age_end: 年龄结束
    :param sql: 查询sql
    :return:
    """
    if start_time is None:
        start_time = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
    if end_time is None:
        end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    df = CONNECTOR.query(
        sql.format(product_serial_code=product_serial_code, start_time=start_time, end_time=end_time),
        show_spinner='查询中...', ttl=10)
    df.rename(columns={'name': '产品系列', 'total_num': '总数', 'ocr_num': 'OCR引用数', 'ocr_rate': 'OCR引用率'},
              inplace=True)
    return df



def get_ocr_medical_person(product_serial_code, start_time=None, end_time=None, sql=None):
    """
    获取审核人员就诊ocr引用情况
    :param product_code: 产品code
    :param start_date: 数据开始日期
    :param end_date: 数据结束日期
    :param age_start: 年龄起始
    :param age_end: 年龄结束
    :param sql: 查询sql
    :return:
    """
    if sql is None:
        sql = [query_sql('SQL_OCR_MEDICAL_PER_PERSON'),query_sql('SQL_INITIAL_AUDIT_REJECT_BY_SELLER'),query_sql('SQL_INITIAL_AUDIT_CASE_MEDICAL')]
    if start_time is None:
        start_time = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
    if end_time is None:
        end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    df = CONNECTOR.query(
        sql[0].format(product_serial_code=product_serial_code, start_time=start_time, end_time=end_time),
        show_spinner='查询中...', ttl=10)
    df.rename(columns={'fullname':'姓名','name': '产品系列', 'total_num': '总数', 'ocr_num': 'OCR引用数', 'ocr_rate': 'OCR引用率'},
              inplace=True)


    df_seller_reject = CONNECTOR.query(sql[1].format(product_serial_code=product_serial_code),show_spinner='查询中...', ttl=10)

    df_total = CONNECTOR.query(sql[2].format(product_serial_code=product_serial_code, start_time=start_time, end_time=end_time), show_spinner='查询中...',
                                       ttl=10)
    df_seller_reject['claim_id'] = df_seller_reject['claim_id'].astype(str)
    df_seller_reject['claim_id_shift'] = df_seller_reject['claim_id'].shift(-1)
    df_seller_reject['status_shift'] = df_seller_reject['status'].shift(-1)
    # 只取claim_id 等于claim_id_shift 且 status 等于 WAIT_REVIEW 且 status_shift 等于 INSURANCE_COMPANY_REJECTED 的数据
    df_seller_reject = df_seller_reject[(df_seller_reject['claim_id'] == df_seller_reject['claim_id_shift']) & (
            df_seller_reject['status'] == 'WAIT_REVIEW') & (df_seller_reject['status_shift'] == 'INSURANCE_COMPANY_REJECTED')]

    df_total = sqldf("select a.*,b.status_shift from df_total a left join df_seller_reject b on a.claim_id = b.claim_id and a.operator_name = b.operator_name"
                     " and a.ice_create_time = b.ice_create_time")
    # 只取icm_create_time与ice_create_time在同一天的记录
    df_total['icm_create_time'] = pd.to_datetime(df_total['icm_create_time'])
    df_total['ice_create_time'] = pd.to_datetime(df_total['ice_create_time'])
    df_total = df_total[(df_total['icm_create_time'].dt.date == df_total['ice_create_time'].dt.date)]
    df_reject_person = df_total[df_total['status_shift'] == 'INSURANCE_COMPANY_REJECTED']
    df_reject_person['reject_num'] = df_reject_person.groupby(['name', 'fullname'])['icm_id'].transform(lambda x: x.dropna().nunique())
    df_reject_person = df_reject_person[['fullname','reject_num']].drop_duplicates()
    df_reject_person.rename(columns={'fullname':'姓名', 'reject_num': '保司驳回数量'},inplace=True)
    df = pd.merge(df, df_reject_person, on='姓名', how='left')
    df.fillna(0, inplace=True)
    df['保司驳回率'] = df.apply(lambda x: round(x['保司驳回数量'] / x['总数'] * 100, 2) if x['总数'] > 0 else 0, axis=1)
    return df



def get_ocr_invoice(product_serial_code, start_time=None, end_time=None, sql=query_sql('SQL_OCR_INVOICE')):
    """
    获取发票ocr引用情况
    :param product_code: 产品code
    :param start_date: 数据开始日期
    :param end_date: 数据结束日期
    :param age_start: 年龄起始
    :param age_end: 年龄结束
    :param sql: 查询sql
    :return:
    """
    if start_time is None:
        start_time = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
    if end_time is None:
        end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    df = CONNECTOR.query(
        sql.format(product_serial_code=product_serial_code, start_time=start_time, end_time=end_time),
        show_spinner='查询中...', ttl=10)
    df.rename(columns={'name': '产品系列', 'total_num': '总数', 'ocr_num': 'OCR引用数', 'ocr_rate': 'OCR引用率'},
              inplace=True)
    return df


def get_ocr_invoice_person(product_serial_code, start_time=None, end_time=None, sql=None):
    """
    获取审核人员发票ocr引用情况
    :param product_code: 产品code
    :param start_date: 数据开始日期
    :param end_date: 数据结束日期
    :param age_start: 年龄起始
    :param age_end: 年龄结束
    :param sql: 查询sql
    :return:
    """
    if sql is None:
        sql = [query_sql('SQL_OCR_INVOICE_PER_PERSON'),query_sql('SQL_INITIAL_AUDIT_REJECT_BY_SELLER'),query_sql('SQL_INITIAL_AUDIT_CASE_INVOICE')]
    if start_time is None:
        start_time = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
    if end_time is None:
        end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    df = CONNECTOR.query(
        sql[0].format(product_serial_code=product_serial_code, start_time=start_time, end_time=end_time),
        show_spinner='查询中...', ttl=10)
    df.rename(columns={'fullname':'姓名','name': '产品系列', 'total_num': '总数', 'ocr_num': 'OCR引用数', 'ocr_rate': 'OCR引用率'},
              inplace=True)

    df_seller_reject = CONNECTOR.query(sql[1].format(product_serial_code=product_serial_code), show_spinner='查询中...',
                                       ttl=10)

    df_total = CONNECTOR.query(
        sql[2].format(product_serial_code=product_serial_code, start_time=start_time, end_time=end_time),
        show_spinner='查询中...',
        ttl=10)
    df_seller_reject['claim_id'] = df_seller_reject['claim_id'].astype(str)
    df_seller_reject['claim_id_shift'] = df_seller_reject['claim_id'].shift(-1)
    df_seller_reject['status_shift'] = df_seller_reject['status'].shift(-1)
    # 只取claim_id 等于claim_id_shift 且 status 等于 WAIT_REVIEW 且 status_shift 等于 INSURANCE_COMPANY_REJECTED 的数据
    df_seller_reject = df_seller_reject[(df_seller_reject['claim_id'] == df_seller_reject['claim_id_shift']) & (
            df_seller_reject['status'] == 'WAIT_REVIEW') & (
                                                    df_seller_reject['status_shift'] == 'INSURANCE_COMPANY_REJECTED')]

    df_total = sqldf(
        "select a.*,b.status_shift from df_total a left join df_seller_reject b on a.claim_id = b.claim_id and a.operator_name = b.operator_name"
        " and a.ice_create_time = b.ice_create_time")
    # 只取icm_create_time与ice_create_time在同一天的记录
    df_total['ici_create_time'] = pd.to_datetime(df_total['ici_create_time'])
    df_total['ice_create_time'] = pd.to_datetime(df_total['ice_create_time'])
    df_total = df_total[(df_total['ici_create_time'].dt.date == df_total['ice_create_time'].dt.date)]
    df_reject_person = df_total[df_total['status_shift'] == 'INSURANCE_COMPANY_REJECTED']
    df_reject_person['reject_num'] = df_reject_person.groupby(['name', 'fullname'])['ici_id'].transform(
        lambda x: x.dropna().nunique())
    df_reject_person = df_reject_person[['fullname', 'reject_num']].drop_duplicates()
    df_reject_person.rename(columns={'fullname': '姓名', 'reject_num': '保司驳回数量'}, inplace=True)
    df = pd.merge(df, df_reject_person, on='姓名', how='left')
    df.fillna(0, inplace=True)
    df['保司驳回率'] = df.apply(lambda x: round(x['保司驳回数量'] / x['总数'] * 100, 2) if x['总数'] > 0 else 0, axis=1)

    return df



def get_ocr_medical_daily(product_serial_code, start_time=None, end_time=None, remove_zero=True, sample_type='日',
                          sql=query_sql('SQL_OCR_MEDICAL_DAILY')):
    """
    获取就诊ocr日度引用情况
    :param product_code: 产品code
    :param start_date: 数据开始日期
    :param end_date: 数据结束日期
    :param age_start: 年龄起始
    :param age_end: 年龄结束
    :param sql: 查询sql
    :return:
    """
    if start_time is None:
        start_time = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
    if end_time is None:
        end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    df = CONNECTOR.query(
        sql.format(product_serial_code=product_serial_code, start_time=start_time, end_time=end_time),
        show_spinner='查询中...', ttl=10)
    df.rename(columns={'create_date': '日期', 'name': '产品系列', 'total_num': '总数', 'ocr_num': 'OCR引用数',
                       'ocr_rate': 'OCR引用率'}, inplace=True)

    # 获取完整的日期序列，补充完整的人员、日期数据
    start_date = start_time.split(' ')[0]
    end_date = end_time.split(' ')[0]
    full_date_range = pd.date_range(start=start_date, end=end_date)
    # 合并成完整的日期序列
    df_combinations = pd.MultiIndex.from_product([full_date_range, df['产品系列'].unique()],
                                                 names=['日期', '产品系列'])
    df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
    df_full_combinations['日期'] = pd.to_datetime(df_full_combinations['日期']).dt.date
    df['日期'] = pd.to_datetime(df['日期']).dt.date
    df = pd.merge(df_full_combinations, df, on=['日期', '产品系列'], how='left')
    df['项目'].fillna('就诊', inplace=True)
    df.fillna(0, inplace=True)
    df.sort_values(['产品系列', '日期'], inplace=True)

    df.reset_index(drop=True, inplace=True)
    df_aggregate_data = aggregate_data(df, sample_type)
    df_aggregate_data['OCR引用率'] = df_aggregate_data.apply(
        lambda x: round(x['OCR引用数'] / x['总数'] * 100, 2) if x['总数'] != 0 else 0, axis=1)
    if remove_zero:
        df_aggregate_data = df_aggregate_data[df_aggregate_data['总数'] != 0]
    return df_aggregate_data


def get_ocr_invoice_daily(product_serial_code, start_time=None, end_time=None, remove_zero=True, sample_type='日',
                          sql=query_sql('SQL_OCR_INVOICE_DAILY')):
    """
    获取发票ocr日度引用情况
    :param product_code: 产品code
    :param start_date: 数据开始日期
    :param end_date: 数据结束日期
    :param age_start: 年龄起始
    :param age_end: 年龄结束
    :param sql: 查询sql
    :return:
    """
    if start_time is None:
        start_time = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
    if end_time is None:
        end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    df = CONNECTOR.query(
        sql.format(product_serial_code=product_serial_code, start_time=start_time, end_time=end_time),
        show_spinner='查询中...', ttl=10)
    df.rename(columns={'create_date': '日期', 'name': '产品系列', 'total_num': '总数', 'ocr_num': 'OCR引用数',
                       'ocr_rate': 'OCR引用率'}, inplace=True)
    # 获取完整的日期序列，补充完整的人员、日期数据
    start_date = start_time.split(' ')[0]
    end_date = end_time.split(' ')[0]
    full_date_range = pd.date_range(start=start_date, end=end_date)
    # 合并成完整的日期序列
    df_combinations = pd.MultiIndex.from_product([full_date_range, df['产品系列'].unique()],
                                                 names=['日期', '产品系列'])
    df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
    df_full_combinations['日期'] = pd.to_datetime(df_full_combinations['日期']).dt.date
    df['日期'] = pd.to_datetime(df['日期']).dt.date
    df = pd.merge(df_full_combinations, df, on=['日期', '产品系列'], how='left')
    df['项目'].fillna('发票', inplace=True)
    df.fillna(0, inplace=True)
    df.sort_values(['产品系列', '日期'], inplace=True)

    df.reset_index(drop=True, inplace=True)
    df_aggregate_data = aggregate_data(df, sample_type)
    df_aggregate_data['OCR引用率'] = df_aggregate_data.apply(
        lambda x: round(x['OCR引用数'] / x['总数'] * 100, 2) if x['总数'] != 0 else 0, axis=1)
    if remove_zero:
        df_aggregate_data = df_aggregate_data[df_aggregate_data['总数'] != 0]
    return df_aggregate_data


def calculate_accuracy_stats(group):
    """
    计算准确率统计信息。
    根据给定的数据组，计算每个'is_same'列的准确率，并返回这些列的统计信息。
    参数:
    group: DataFrame类型，包含多个列，其中一些列的名称包含'is_same'。
    返回:
    pd.Series类型，包含每个'is_same'列的准确率相关统计信息。
    """
    # 初始化一个空字典来存储统计信息
    stats = {}
    # 筛选出所有包含'is_same'的列名
    is_same_cols = [col for col in group.columns if 'is_same' in col]
    # 遍历每个'is_same'列，计算并存储其准确率统计信息
    for col in is_same_cols:
        # 计算值为1的数量，作为准确率的分子
        ones = (group[col] == 1).sum()
        # 计算总的记录数，作为准确率的分母
        total = group[col].count()
        # 计算准确率
        accuracy = ones / total
        # 将计算结果存储到stats字典中
        stats[col + '_正确数量'] = ones
        stats[col + '_总数量'] = total
        stats[col + '_准确率'] = accuracy
    # 返回包含所有统计信息的Series对象
    return pd.Series(stats)


def transform_accuracy_df(df, is_daily=False):
    """
    将包含多个准确率统计信息的DataFrame转换为所需的格式。

    参数:
    df: DataFrame类型，包含产品系列名称和多个'is_same'列的准确率统计信息。

    返回:
    DataFrame类型，转换后的格式包含name、column_name、正确数量、总数量、准确率。
    """
    # 初始化一个空列表来存储转换后的数据
    transformed_data = []

    # 遍历每一行数据
    for index, row in df.iterrows():
        if is_daily:
            name = row['name']
            create_date = row['create_date']
        else:
            name = row['name']
        # 遍历每一列，提取包含'准确率'的列名
        for col in df.columns:
            if '准确率' in col:
                column_name = col.replace('_准确率', '')
                correct_count = row[column_name + '_正确数量']
                total_count = row[column_name + '_总数量']
                accuracy = row[col]
                if is_daily:
                    transformed_data.append({
                        'name': name,
                        'create_date': create_date,
                        'column_name': column_name,
                        '正确数量': correct_count,
                        '总数量': total_count,
                        '准确率': accuracy
                    })
                else:
                    transformed_data.append({
                        'name': name,
                        'column_name': column_name,
                        '正确数量': correct_count,
                        '总数量': total_count,
                        '准确率': accuracy
                    })

    # 将转换后的数据列表转换为DataFrame
    transformed_df = pd.DataFrame(transformed_data)
    return transformed_df


def get_compare_excel(path_name):
    # 假设你的数据在一个DataFrame中
    data = pd.read_excel(path_name)

    # 创建一个颜色映射字典
    color_map = {
        0: PatternFill(start_color='FFC7CE', end_color='FFC7CE', fill_type='solid'),  # 淡红色
        2: PatternFill(start_color='FFFF99', end_color='FFFF99', fill_type='solid')  # 淡黄色
    }

    # 添加底色，便于人工识别
    writer = load_workbook(path_name)
    # 获取活动的工作表
    worksheet = writer['Sheet1']
    # 遍历每一列
    for col_idx, col_name in enumerate(data.columns):
        # 检查is_same字段是否存在
        if f'{col_name}_is_same' in data.columns:
            # 获取is_same字段的列索引
            is_same_col_idx = data.columns.get_loc(f'{col_name}_is_same')

            # 检查_ocr字段是否存在
            if f'{col_name}_ocr' in data.columns:
                # 获取_ocr字段的列索引
                ocr_col_idx = data.columns.get_loc(f'{col_name}_ocr')

                # 遍历每一行
                for row_idx in range(2, len(data) + 2):  # 从第二行开始，因为第一行是列名
                    # 获取is_same字段的值
                    is_same_value = worksheet.cell(row=row_idx, column=is_same_col_idx + 1).value

                    # 根据is_same字段的值设置颜色
                    if is_same_value in color_map:
                        # 设置原始字段的颜色
                        worksheet.cell(row=row_idx, column=col_idx + 1).fill = color_map[is_same_value]

                        # 设置_ocr字段的颜色
                        worksheet.cell(row=row_idx, column=ocr_col_idx + 1).fill = color_map[is_same_value]
    # 保存工作簿
    writer.save(path_name)


def get_ocr_medical_accuracy_info(product_serial_code, start_time=None, end_time=None, remove_zero=True,
                                  sample_type='日', sql=query_sql('SQL_OCR_MEDICAL_DETAIL')):
    """
    获取ocr就诊准确率信息
    :return:
    """
    if start_time is None:
        start_time = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
    if end_time is None:
        end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    df = CONNECTOR.query(
        sql.format(product_serial_code=product_serial_code, start_time=start_time, end_time=end_time),
        show_spinner='查询中...', ttl=10)
    if df.empty:
        return pd.DataFrame(),pd.DataFrame()

    df['detail'] = df['detail'].apply(safe_json_load)
    # 将detail 字段中的字典转成df的列，以key为列名，列名并拼接detail ，并拼接到df_medical中
    detail_df = pd.json_normalize(df['detail'], max_level=0)

    if 'jiangsu_yhb' in product_serial_code:
        compare_list = ['treatment_date', 'treatment_end_date', 'hospital', 'medical_type', 'ecdemic','ecdemic_record','referral_record','chronic_disease_record']
    else:
        compare_list = ['treatment_date', 'treatment_end_date', 'hospital', 'medical_type', 'ecdemic']

    detail_df = detail_df[compare_list]
    detail_df.columns = [col + '_ocr' for col in detail_df.columns]
    detail_df['medical_type_ocr'] = detail_df['medical_type_ocr'].apply(lambda x: x.get('code', None))
    detail_df['ecdemic_ocr'] = detail_df['ecdemic_ocr'].apply(lambda x: 1 if x else 0)
    if 'ecdemic_record_ocr' in detail_df.columns:
        detail_df['ecdemic_record_ocr'] = detail_df['ecdemic_record_ocr'].apply(lambda x: 1 if x else 0)
    if'referral_record_ocr' in detail_df.columns:
        detail_df['referral_record_ocr'] = detail_df['referral_record_ocr'].apply(lambda x: 1 if x else 0)
    if 'chronic_disease_record_ocr' in detail_df.columns:
        detail_df['chronic_disease_record_ocr'] = detail_df['chronic_disease_record_ocr'].apply(lambda x: 1 if x else 0)


    df_total = pd.concat([df, detail_df], axis=1)

    df_total.drop(['detail'], axis=1, inplace=True)
    # df_total 根据列名排序name放在首位
    df_total = df_total[
        ['name', 'create_date'] + sorted([col for col in df_total.columns if col not in ['name', 'create_date']])]
    # 含date的列都设置为str格式
    for col in df_total.columns:
        if 'date' in col:
            df_total[col] = df_total[col].astype(str)
    # 对于含ocr_的列与不含ocr_的列进行值比较，如果不含ocr_的列的值不为空，则比对值，如果全部一样，新增一列is_same，值为1，否则为0

    # 创建 is_same 列
    for i in compare_list:
        df_total[i + '_is_same'] = 0

    # df_total['is_same'] = 1
    # 对于含ocr_的列与不含ocr_的列进行值比较
    # for col in ['treatment_date', 'treatment_end_date', 'hospital', 'medical_type', 'ecdemic']:
    # for i in range(len(df_total)):
    #     for col in compare_list:
    #         if df_total.loc[i, col] != df_total.loc[i, col + '_ocr']:
    #             df_total.loc[i, 'is_same'] = 0

    for i in range(len(df_total)):
        for col in compare_list:
            if df_total.loc[i, col] == df_total.loc[i, col + '_ocr']:
                df_total.loc[i, col + '_is_same'] = 1

    accuracy_df = df_total.groupby('name').apply(calculate_accuracy_stats).reset_index()

    df_total_daily = df_total.groupby(['name', 'create_date']).apply(calculate_accuracy_stats).reset_index()
    # 提取accuracy_df中含正确数量、总数量、准确率的列，将原来的列名转成column_name列，列为正确数量、总数量、准确率的列名
    df_transformed = transform_accuracy_df(accuracy_df)
    df_transformed.rename(columns={'column_name': '字段', 'name': '产品系列'}, inplace=True)
    df_transformed['准确率'] = df_transformed['准确率'].apply(lambda x: round(x * 100, 2))
    df_transformed_daily = transform_accuracy_df(df_total_daily, is_daily=True)
    df_transformed_daily.rename(columns={'name': '产品系列', 'create_date': '日期', 'column_name': '字段'},
                                inplace=True)
    df_transformed_daily['准确率'] = df_transformed_daily['准确率'].apply(lambda x: round(x * 100, 2))
    name = '就诊ocr对比明细.xlsx'
    path_name = Path.cwd().joinpath('temp_files').joinpath(name)
    df_total['claim_id'] = df_total['claim_id'].astype(str)
    df_total = df_total[
        ['name', 'claim_id', 'ocr_medical_id','create_date'] + sorted(
            [col for col in df_total.columns if col not in ['name', 'claim_id', 'ocr_medical_id','create_date']])]
    # df_excel 将列名含is_same的列放在最后，其他的列放在前面
    df_total = df_total[sorted(df_total.columns, key=lambda x: 1 if x.endswith('is_same') else 0)]

    df_total.to_excel(path_name, index=False)
    get_compare_excel(path_name)
    df_transformed_daily = aggregate_data_v2(df_transformed_daily, sample_type)
    if remove_zero:
        df_transformed_daily = df_transformed_daily[df_transformed_daily['总数量'] != 0]
    # 如果产品系列不等于江苏医惠保，则删除字段值为ecdemic_record、referral_record、chronic_disease_record的行
    df_transformed = df_transformed[
        (df_transformed['产品系列'] == '江苏医惠保') |
        (~df_transformed['字段'].isin(['ecdemic_record_is_same', 'referral_record_is_same', 'chronic_disease_record_is_same']))
        ]
    df_transformed_daily = df_transformed_daily[
        (df_transformed_daily['产品系列'].isin(['江苏医惠保'])) |
        (~df_transformed_daily['字段'].isin(['ecdemic_record_is_same', 'referral_record_is_same', 'chronic_disease_record_is_same']))
        ]
    return df_transformed, df_transformed_daily


def get_ocr_invoice_detail(product_serial_code, start_time=None, end_time=None,
                           sql=query_sql('SQL_OCR_INVOICE_DETAIL')):
    """
    获取ocr发票明细
    :param product_serial_code:
    :param start_time:
    :param end_time:
    :param sql:
    :return:
    """
    if start_time is None:
        start_time = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
    if end_time is None:
        end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    df = CONNECTOR.query(
        sql.format(product_serial_code=product_serial_code, start_time=start_time, end_time=end_time),
        show_spinner='查询中...', ttl=120)
    return df


def get_ocr_invoice_accuracy_info(df, product_serial_code_list):
    """
    获取ocr发票准确率信息
    :return:
    """
    invoice_sql = """
      SELECT distinct 
                rd.CODE code,
                rd.NAME name
    FROM
        product_serial ps
        JOIN resource_dict rd ON rd.entity_id = ps.id
        AND rd.entity_type = 'PRODUCT_SERIAL'
        AND rd.resource_dict_type = 'INVOICE_FIELD'
    where ps.code= '{product_serial_code}'
    """
    accuracy_df_total = pd.DataFrame(
        columns=['column', 'correct_count', 'total_count', 'accuracy', 'correct_count_v1', 'total_count_v1', 'accuracy_v1',
                 'name'])
    df_excel = pd.DataFrame()
    for i in product_serial_code_list:
        df_invoice = CONNECTOR.query(invoice_sql.format(product_serial_code=i), show_spinner='查询中...', ttl=10)
        df_medical = df.copy()
        df_medical = df_medical[df_medical['code'] == i]
        if df_medical.empty:
            continue
        df_medical.reset_index(drop=True, inplace=True)
        df_medical['detail'] = df_medical['detail'].apply(safe_json_load)
        # 将detail 字段中的字典转成df的列，以key为列名，列名并拼接detail ，并拼接到df_medical中
        detail_df = pd.json_normalize(df_medical['detail'], max_level=0)
        detail_map_df = pd.json_normalize(detail_df['detail_map'], max_level=0)
        detail_matching_columns = detail_map_df.columns[detail_map_df.columns.isin(df_invoice['code'])]
        detail_map_df = detail_map_df[detail_matching_columns]
        detail_map_df = pd.concat([detail_map_df, detail_df[['amount', 'number']]], axis=1)
        detail_map_df.columns = [col + '_ocr' for col in detail_map_df.columns]
        # df_medical 取出name,code列和列名等于df_invoice中code值得列
        # 提取 df_medical 中的 name 和 code 列
        df_medical_new = df_medical[['name', 'code', 'claim_id', 'ocr_invoice_id', 'amount', 'number']]
        df_medical_new['claim_id'] = df_medical_new['claim_id'].astype(str)
        df_medical_new['ocr_invoice_id'] = df_medical_new['ocr_invoice_id'].astype(str)
        # 提取 df_medical 中列名与 df_invoice 中 code 列的值匹配的列
        matching_columns = df_medical.columns[df_medical.columns.isin(df_invoice['code'])]
        df_medical_new = df_medical_new.join(df_medical[matching_columns])
        df_total = pd.concat([df_medical_new, detail_map_df], axis=1)
        # df_total 根据列名排序，name、code、claim_id,ocr_invoice_id 放在前面
        priority_cols = ['name', 'code', 'claim_id', 'ocr_invoice_id']

        # 获取剩余的列名，并按字母顺序排序
        remaining_cols = sorted([col for col in df_total.columns if col not in priority_cols])
        # 将优先列和剩余列合并
        new_order = priority_cols + remaining_cols
        # 按照新的列顺序重新排列 DataFrame
        df_total = df_total.loc[:, new_order]

        # df_total 根据df_invoice['code']的值拼接 is_same，进行列添加，置为0
        df_total['is_same'] = 1
        df_total['amount_is_same'] = 0
        df_total['number_is_same'] = 0
        for col in df_invoice['code']:
            df_total[col + '_is_same'] = 0

        # 对于含detail_的列与不含detail_的列进行值比较
        all_cols = df_invoice['code'].tolist()
        all_cols.append('amount')
        all_cols.append('number')
        for j in range(len(df_total)):
            for col in all_cols:
                if col + '_ocr' not in df_total.columns:
                    continue
                elif  col not in df_total.columns:
                    continue
                elif df_total.loc[j, col] != df_total.loc[j, col + '_ocr'] and not pd.isna(
                        df_total.loc[j, col + '_ocr']) \
                        and not pd.isna(df_total.loc[j, col]) and df_total.loc[j, col] != 0 and df_total.loc[
                    j, col + '_ocr'] != 0:
                    df_total.loc[j, 'is_same'] = 0

        for j in range(len(df_total)):
            for col in all_cols:
                # 如果未识别，则置为2
                if col + '_ocr' not in df_total.columns and col in df_total.columns:
                    if np.nan_to_num(pd.to_numeric(df_total.loc[j, col], errors='coerce')) > 0:
                        df_total.loc[j, col + '_is_same'] = 2
                # 如果数据为空，则置为2
                if col + '_ocr' in df_total.columns and col in df_total.columns:
                    if pd.isna(df_total.loc[j, col + '_ocr']) and np.nan_to_num(
                            pd.to_numeric(df_total.loc[j, col], errors='coerce')) > 0:
                        df_total.loc[j, col + '_is_same'] = 2

        for k in range(len(df_total)):
            for col in all_cols:
                if col not in df_total.columns:
                    continue
                elif col + '_ocr' not in df_total.columns:
                    # 如果数据为空或者0，则说明是正确的
                    if pd.isna(df_total.loc[k, col]) or pd.to_numeric(df_total.loc[k, col], errors='coerce') == 0:
                        df_total.loc[k, col + '_is_same'] = 1
                    else:
                        continue
                else:
                    if np.nan_to_num(pd.to_numeric(df_total.loc[k, col], errors='coerce')) == np.nan_to_num(
                            pd.to_numeric(df_total.loc[k, col + '_ocr'], errors='coerce')):
                        df_total.loc[k, col + '_is_same'] = 1
        df_total.reset_index(drop=True, inplace=True)
        # 统计每一种is_same的正确率，1表示正确，0表示错误，如果是2表示未对比，统计用正确/总数-2的数量
        # 创建一个新的 DataFrame 来记录每一种 col+'_is_same' 的正确率
        accuracy_df = pd.DataFrame(columns=['column'])

        # 统计每一种 col+'_is_same' 的正确率
        new_cols = [i + '_is_same' for i in all_cols]
        new_cols.append('is_same')

        # 统计引用率 排除实际为空或者0的行，也就是值必须大于0的行，切对应的is_same 不为2

        for col in all_cols:
            col_is_same = col + '_is_same'
            if col_is_same not in df_total.columns:
                continue
            if col not in df_total.columns:
                continue
            df_total[col] = pd.to_numeric(df_total[col], errors='coerce')
            # 计算正确率，排除未对比的情况
            correct_count = (df_total[col_is_same] == 1).sum()
            total_count = (df_total[col_is_same]).count()  # 数据表有效数据

            correct_count_v1 = (df_total[np.nan_to_num(df_total[col])>0][col_is_same] == 1).sum()
            total_count_v1 = (df_total[np.nan_to_num(df_total[col])>0][col_is_same]).count()  # 排除值为 2 的行

            accuracy = round(correct_count / total_count * 100, 2)
            accuracy_v1 = round(correct_count_v1 / total_count_v1 * 100, 2)
            if (df_total[col_is_same]).sum() / df_total[col_is_same].count() == 2:
                accuracy = '未识别'


            temp_df = pd.DataFrame(
                [[col, correct_count,  total_count, accuracy, correct_count_v1,total_count_v1,accuracy_v1]],
                columns=['column', 'correct_count', 'total_count', 'accuracy', 'correct_count_v1', 'total_count_v1', 'accuracy_v1'])
            accuracy_df = pd.concat([accuracy_df, temp_df], axis=0)
        accuracy_df['name'] = df_total['name'].iloc[0]
        accuracy_df_total = pd.concat([accuracy_df_total, accuracy_df], axis=0)
        df_excel = pd.concat([df_excel, df_total], axis=0)
    if df_excel.empty:
        return pd.DataFrame()
    name = '发票ocr对比明细.xlsx'
    path_name = Path.cwd().joinpath('temp_files').joinpath(name)
    df_excel = df_excel[
        ['name','code','claim_id', 'ocr_invoice_id'] + sorted([col for col in df_excel.columns if col not in ['name','code','claim_id', 'ocr_invoice_id']])]
    # df_excel 将列名含is_same的列放在最后，其他的列放在前面
    df_excel = df_excel[sorted(df_excel.columns, key=lambda x: 1 if x.endswith('is_same') else 0)]
    df_excel.to_excel(path_name, index=False)
    get_compare_excel(path_name)


    accuracy_df_total.rename(
        columns={'column': '字段','name': '产品系列', 'correct_count': '正确数量', 'total_count': '总数量',
                 'correct_count_v1': '正确数量V1', 'total_count_v1': '总数量V1','accuracy':'准确率',
                 'accuracy_v1': '准确率V1'}, inplace=True)
    accuracy_df_total.reset_index(drop=True, inplace=True)
    accuracy_df_total['准确率'] = accuracy_df_total.apply(
        lambda x: 100 if x['总数量'] == 0 and x['正确数量'] == 0 else x['准确率'], axis=1)
    accuracy_df_total['准确率V1'] = accuracy_df_total.apply(
        lambda x: 100 if x['总数量V1'] == 0 and x['正确数量V1'] == 0 else x['准确率V1'],axis=1)
    accuracy_df_total['准确率'] = accuracy_df_total.apply(
        lambda x: 0 if x['总数量'] == 0 and x['正确数量'] > 0 else x['准确率'], axis=1)
    accuracy_df_total['准确率V1'] = accuracy_df_total.apply(
        lambda x: 0 if x['总数量V1'] == 0 and x['正确数量V1'] > 0 else x['准确率V1'],axis=1)
    accuracy_df_total.fillna(np.nan, inplace=True)
    accuracy_df_total = accuracy_df_total.astype(str)
    # 产品名称放在首列
    accuracy_df_total = accuracy_df_total[['产品系列'] + accuracy_df_total.columns[:-1].tolist()]
    accuracy_df_total.replace('nan', np.nan, inplace=True)
    return accuracy_df_total


def get_ocr_invoice_accuracy_info_daily(df, product_serial_code_list, sample_type='日'):
    """
    获取ocr发票准确率信息，按日统计
    :return:
    """
    invoice_sql = """
    SELECT distinct 
        rd.CODE code,
        rd.NAME name
    FROM
        product_serial ps
    JOIN resource_dict rd ON rd.entity_id = ps.id
    AND rd.entity_type = 'PRODUCT_SERIAL'
    AND rd.resource_dict_type = 'INVOICE_FIELD'
    where ps.code= '{product_serial_code}'
    """
    accuracy_df_total = pd.DataFrame(
        columns=['column', 'create_date', 'correct_count', 'total_count', 'accuracy', 'correct_count_v1', 'total_count_v1', 'accuracy_v1', 'name'])

    for i in product_serial_code_list:
        df_invoice = CONNECTOR.query(invoice_sql.format(product_serial_code=i), show_spinner='查询中...', ttl=10)
        df_medical = df.copy()
        df_medical = df_medical[df_medical['code'] == i]
        if df_medical.empty:
            continue
        df_medical.reset_index(drop=True, inplace=True)
        df_medical['detail'] = df_medical['detail'].apply(safe_json_load)
        # 将detail 字段中的字典转成df的列，以key为列名，列名并拼接detail ，并拼接到df_medical中
        detail_df = pd.json_normalize(df_medical['detail'], max_level=0)
        detail_map_df = pd.json_normalize(detail_df['detail_map'], max_level=0)
        detail_matching_columns = detail_map_df.columns[detail_map_df.columns.isin(df_invoice['code'])]
        detail_map_df = detail_map_df[detail_matching_columns]
        detail_map_df = pd.concat([detail_map_df, detail_df[['amount', 'number']]], axis=1)
        detail_map_df.columns = [col + '_ocr' for col in detail_map_df.columns]
        # df_medical 取出name,code列和列名等于df_invoice中code值得列
        # 提取 df_medical 中的 name 和 code 列
        df_medical_new = df_medical[['name', 'create_date', 'code', 'claim_id', 'ocr_invoice_id', 'amount', 'number']]
        df_medical_new['claim_id'] = df_medical_new['claim_id'].astype(str)
        df_medical_new['ocr_invoice_id'] = df_medical_new['ocr_invoice_id'].astype(str)
        # 提取 df_medical 中列名与 df_invoice 中 code 列的值匹配的列
        matching_columns = df_medical.columns[df_medical.columns.isin(df_invoice['code'])]
        df_medical_new = df_medical_new.join(df_medical[matching_columns])
        df_total = pd.concat([df_medical_new, detail_map_df], axis=1)
        # df_total 根据列名排序，name、code、claim_id,ocr_invoice_id 放在前面
        priority_cols = ['name', 'create_date', 'code', 'claim_id', 'ocr_invoice_id']

        # 获取剩余的列名，并按字母顺序排序
        remaining_cols = sorted([col for col in df_total.columns if col not in priority_cols])
        # 将优先列和剩余列合并
        new_order = priority_cols + remaining_cols
        # 按照新的列顺序重新排列 DataFrame
        df_total = df_total.loc[:, new_order]

        # df_total 根据df_invoice['code']的值拼接 is_same，进行列添加，置为0
        df_total['is_same'] = 1
        df_total['amount_is_same'] = 0
        df_total['number_is_same'] = 0
        for col in df_invoice['code']:
            df_total[col + '_is_same'] = 0

        # 对于含detail_的列与不含detail_的列进行值比较
        all_cols = df_invoice['code'].tolist()
        all_cols.append('amount')
        all_cols.append('number')

        for j in range(len(df_total)):
            for col in all_cols:
                if col + '_ocr' not in df_total.columns:
                    continue
                elif col not in df_total.columns:
                    continue
                elif df_total.loc[j, col] != df_total.loc[j, col + '_ocr'] and not pd.isna(df_total.loc[j, col + '_ocr'])\
                        and not pd.isna(df_total.loc[j, col]) and df_total.loc[j, col]!=0 and df_total.loc[j, col + '_ocr']!=0:
                    df_total.loc[j, 'is_same'] = 0

        for j in range(len(df_total)):
            for col in all_cols:
                # 如果未识别，则置为2
                if col + '_ocr' not in df_total.columns and col in df_total.columns and np.nan_to_num(pd.to_numeric(df_total.loc[j, col], errors='coerce')) >0:
                    df_total.loc[j, col + '_is_same'] = 2
                # 如果数据为空，则置为2
                if col + '_ocr' in df_total.columns and col in df_total.columns:
                    if pd.isna(df_total.loc[j, col + '_ocr']) and np.nan_to_num(pd.to_numeric(df_total.loc[j, col], errors='coerce')) >0:
                        df_total.loc[j, col + '_is_same'] = 2

        for k in range(len(df_total)):
            for col in all_cols:
                if col not in df_total.columns:
                    continue
                elif col + '_ocr' not in df_total.columns:
                    # 如果数据为空或者0，则说明是正确的
                    if pd.isna(df_total.loc[k, col]) or pd.to_numeric(df_total.loc[k, col], errors='coerce') == 0:
                        df_total.loc[k, col + '_is_same'] = 1
                    else:
                        continue
                elif col not in df_total.columns:
                    continue
                else:
                    if np.nan_to_num(pd.to_numeric(df_total.loc[k, col], errors='coerce')) == np.nan_to_num(pd.to_numeric(df_total.loc[k, col+ '_ocr'], errors='coerce')):
                        df_total.loc[k, col + '_is_same'] = 1

        df_total.sort_values(by=['create_date'], inplace=True)
        df_total.reset_index(drop=True, inplace=True)
        # 统计每一种is_same的正确率，1表示正确，0表示错误，如果是2表示未对比，统计用正确/总数-2的数量
        # 创建一个新的 DataFrame 来记录每一种 col+'_is_same' 的正确率
        accuracy_df = pd.DataFrame(columns=['column'])

        # 统计每一种 col+'_is_same' 的正确率
        new_cols = [i + '_is_same' for i in all_cols]
        new_cols.append('is_same')

        # 统计引用率 排除实际为空或者0的行，也就是值必须大于0的行，切对应的is_same 不为2

        for col in all_cols:
            col_is_same = col + '_is_same'
            if col_is_same not in df_total.columns:
                continue
            if col not in df_total.columns:
                continue
            df_total[col] = pd.to_numeric(df_total[col], errors='coerce')
            # 计算正确率，排除未对比的情况
            correct_count = df_total.groupby('create_date')[col_is_same].apply(
                lambda x: (x == 1).sum()).reset_index(name='correct_count')

            total_count = df_total.groupby('create_date')[col_is_same].apply(
                lambda x: x.count()).reset_index(name='total_count')

            # 筛选出 df_total[col] 中非零的行
            df_non_zero = df_total[np.nan_to_num(df_total[col]) > 0]

            # 按 create_date 分组，并计算每个分组中 col_is_same 列值为 1 的数量
            correct_count_v1 = df_non_zero.groupby('create_date')[col_is_same].apply(
                lambda x: (x == 1).sum()).reset_index(name='correct_count_v1')
            total_count_v1 = df_non_zero.groupby('create_date')[col_is_same].apply(
                lambda x: x.count()).reset_index(name='total_count_v1')

            temp_df = pd.merge(correct_count, total_count, on='create_date', how='outer')
            temp_df = pd.merge(temp_df, correct_count_v1, on='create_date', how='outer')
            temp_df = pd.merge(temp_df, total_count_v1, on='create_date', how='outer')
            temp_df.fillna(0, inplace=True)
            temp_df['column'] = col

            temp_df['accuracy'] = temp_df.apply(
                lambda x: round(x['correct_count'] / x['total_count'] * 100, 2) if x['total_count'] > 0 else np.nan, axis=1)
            temp_df['accuracy_v1'] = temp_df.apply(
                lambda x: round(x['correct_count_v1'] / x['total_count_v1'] * 100, 2) if x['total_count_v1'] > 0 else np.nan,
                axis=1)

            accuracy_df = pd.concat([accuracy_df, temp_df], axis=0)
        accuracy_df['name'] = df_total['name'].iloc[0]
        accuracy_df_total = pd.concat([accuracy_df_total, accuracy_df], axis=0)

    accuracy_df_total.rename(
        columns={'column': '字段','name': '产品系列', 'correct_count': '正确数量', 'total_count': '总数量',
                 'correct_count_v1': '正确数量V1', 'total_count_v1': '总数量V1','accuracy':'准确率',
                 'accuracy_v1': '准确率V1', 'create_date': '日期'}, inplace=True)
    accuracy_df_total.fillna(np.nan, inplace=True)
    accuracy_df_total = accuracy_df_total.astype(str)

    # # 产品名称放在首列
    accuracy_df_total = accuracy_df_total[
        ['产品系列', '日期', '字段', '正确数量', '总数量', '准确率','正确数量V1', '总数量V1','准确率V1']]
    accuracy_df_total.sort_values(by=['产品系列', '日期', '字段'], inplace=True)
    accuracy_df_total.reset_index(drop=True, inplace=True)
    accuracy_df_total.replace('nan', np.nan, inplace=True)

    accuracy_df_total = aggregate_data_v3(accuracy_df_total, sample_type)
    accuracy_df_total['准确率'] = accuracy_df_total.apply(
        lambda x: 100 if x['总数量'] == 0 and x['正确数量'] == 0 else x['准确率'], axis=1)
    accuracy_df_total['准确率V1'] = accuracy_df_total.apply(
        lambda x: 100 if x['总数量V1'] == 0 and x['正确数量V1'] == 0 else x['准确率V1'], axis=1)

    accuracy_df_total['准确率'] = accuracy_df_total.apply(
        lambda x: 0 if x['总数量'] == 0 and x['正确数量'] > 0 else x['准确率'], axis=1)
    accuracy_df_total['准确率V1'] = accuracy_df_total.apply(
        lambda x: 0 if x['总数量V1'] == 0 and x['正确数量V1'] > 0 else x['准确率V1'], axis=1)
    accuracy_df_total = accuracy_df_total[
        ['产品系列', '日期', '字段', '正确数量', '总数量', '准确率','正确数量V1', '总数量V1','准确率V1']]
    return accuracy_df_total


def st_plot_1(product_serial_code, product_serial_name, start_time, end_time, remove_zero, sample_type):
    """
    就诊ocr引用情况
    :param product_serial_code: 产品系列code
    :param product_serial_name: 产品系列名称
    :param start_time: 数据开始日期
    :param end_time: 数据结束日期
    :param remove_zero: 是否剔除0值
    :return:
    """
    text_write('OCR引用率')
    sub_text_write('OCR整体引用率')
    df_ocr_medical = get_ocr_medical(product_serial_code, start_time, end_time)
    st.dataframe(df_ocr_medical.drop(columns='项目'), hide_index=True, use_container_width=True)
    sub_text_write('OCR日度引用率')
    df_ocr_medical_daily = get_ocr_medical_daily(product_serial_code, start_time, end_time, remove_zero, sample_type)
    st.dataframe(df_ocr_medical_daily.drop(columns='项目'), hide_index=True, use_container_width=True)
    sub_text_write('OCR引用趋势图')
    # 创建一个带有子图的图表
    fig = make_subplots(specs=[[{"secondary_y": True}]])
    # 筛选产品系列
    for product in product_serial_name:
        df_filtered = df_ocr_medical_daily[df_ocr_medical_daily['产品系列'] == product]
        fig.add_trace(
            go.Bar(x=df_filtered['日期'].tolist(), y=df_filtered['总数'].tolist(),
                   name=f'{product}就诊总数', text=df_filtered['总数'].tolist()), secondary_y=False)

        # 添加赔付人数的折线图系列
        fig.add_trace(
            go.Scatter(x=df_filtered['日期'].tolist(), y=df_filtered['OCR引用率'].tolist(),
                       name=f'{product}OCR引用率', mode='lines+markers', yaxis='y2'),
            secondary_y=True
        )
        # 计算OCR引用率的趋势线
        if not df_filtered.empty:
            x = np.arange(len(df_filtered))
            y = df_filtered['OCR引用率'].tolist()
            z = np.polyfit(x, y, 1)  # 计算一次多项式拟合，即线性回归
            p = np.poly1d(z)  # 创建多项式函数

            # 添加趋势线
            fig.add_trace(
                go.Scatter(x=df_filtered['日期'].tolist(), y=p(x),
                           name=f'{product}OCR引用率趋势线', mode='lines', line=dict(dash='dash'), yaxis='y2'),
                secondary_y=True
            )
    fig.update_xaxes(tickformat="%Y-%m-%d")
    # 移除副y轴的网格线
    fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
    # 设置y轴和y次轴的范围模式为从0开始
    fig.update_yaxes(rangemode="tozero", secondary_y=False)
    fig.update_yaxes(rangemode="tozero", secondary_y=True)

    fig.update_layout(title='',
                      xaxis_title='日期',
                      yaxis_title='就诊总数',
                      yaxis2_title="ocr引用率",
                      barmode='group')
    st.plotly_chart(fig, key='st_plot_1')

    sub_text_write('初审人员OCR整体引用情况')
    df_ocr_medical_person = get_ocr_medical_person(product_serial_code, start_time, end_time)
    st.dataframe(df_ocr_medical_person.drop(columns='项目'), hide_index=True, use_container_width=True)
    sub_text_write('初审人员OCR引用分布')
    # 绘制柱状图
    # 创建一个带有子图的图表
    fig = make_subplots(specs=[[{"secondary_y": True}]])
    # 筛选产品系列
    for product in product_serial_name:
        df_filtered = df_ocr_medical_person[df_ocr_medical_person['产品系列'] == product]
        fig.add_trace(
            go.Bar(x=df_filtered['姓名'].tolist(), y=df_filtered['总数'].tolist(),
                   name=f'{product}就诊总数', text=df_filtered['总数'].tolist()), secondary_y=False)

        # 添加赔付人数的折线图系列
        fig.add_trace(
            go.Scatter(x=df_filtered['姓名'].tolist(), y=df_filtered['OCR引用率'].tolist(),
                       name=f'{product}审核人员OCR引用率', mode='lines+markers', yaxis='y2'),
            secondary_y=True
        )
        fig.add_trace(
            go.Scatter(x=df_filtered['姓名'].tolist(), y=df_filtered['保司驳回率'].tolist(),
                       name=f'{product}保司驳回率', mode='lines+markers', yaxis='y2'),
            secondary_y=True
        )
    # 移除副y轴的网格线
    fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
    # 设置y轴和y次轴的范围模式为从0开始
    fig.update_yaxes(rangemode="tozero", secondary_y=False)
    fig.update_yaxes(rangemode="tozero", secondary_y=True)

    fig.update_layout(title='',
                      xaxis_title='',
                      yaxis_title='就诊数量',
                      yaxis2_title="ocr引用率、驳回率",
                      barmode='group')
    st.plotly_chart(fig, key='st_plot_person_1')




def st_plot_2(product_serial_code, product_serial_name, start_time, end_time, remove_zero, sample_type):
    """
    发票ocr引用情况
    :param product_serial_code: 产品系列code
    :param product_serial_name: 产品系列名称
    :param start_time: 数据开始日期
    :param end_time: 数据结束日期
    :param remove_zero: 是否剔除0值
    :return:
    """
    text_write('发票OCR引用情况')
    sub_text_write('发票OCR整体引用情况')
    df_ocr_invoice = get_ocr_invoice(product_serial_code, start_time, end_time)
    st.dataframe(df_ocr_invoice, hide_index=True, use_container_width=True)
    sub_text_write('发票OCR日度引用情况')
    df_ocr_invoice_daily = get_ocr_invoice_daily(product_serial_code, start_time, end_time, remove_zero, sample_type)
    st.dataframe(df_ocr_invoice_daily, hide_index=True, use_container_width=True)
    sub_text_write('发票OCR引用趋势图')
    # 创建一个带有子图的图表
    fig = make_subplots(specs=[[{"secondary_y": True}]])
    # 筛选产品系列
    for product in product_serial_name:
        df_filtered = df_ocr_invoice_daily[df_ocr_invoice_daily['产品系列'] == product]
        fig.add_trace(
            go.Bar(x=df_filtered['日期'].tolist(), y=df_filtered['总数'].tolist(),
                   name=f'{product}发票总数', text=df_filtered['总数'].tolist()), secondary_y=False)

        # 添加赔付人数的折线图系列
        fig.add_trace(
            go.Scatter(x=df_filtered['日期'].tolist(), y=df_filtered['OCR引用率'].tolist(),
                       name=f'{product}OCR引用率', mode='lines+markers', yaxis='y2'),
            secondary_y=True
        )
        # 计算OCR引用率的趋势线
        if not df_filtered.empty:
            x = np.arange(len(df_filtered))
            y = df_filtered['OCR引用率'].tolist()
            z = np.polyfit(x, y, 1)  # 计算一次多项式拟合，即线性回归
            p = np.poly1d(z)  # 创建多项式函数

            # 添加趋势线
            fig.add_trace(
                go.Scatter(x=df_filtered['日期'].tolist(), y=p(x),
                           name=f'{product}OCR引用率趋势线', mode='lines', line=dict(dash='dash'), yaxis='y2'),
                secondary_y=True
            )
    fig.update_xaxes(tickformat="%Y-%m-%d")
    # 移除副y轴的网格线
    fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
    # 设置y轴和y次轴的范围模式为从0开始
    fig.update_yaxes(rangemode="tozero", secondary_y=False)
    fig.update_yaxes(rangemode="tozero", secondary_y=True)

    fig.update_layout(title='',
                      xaxis_title='日期',
                      yaxis_title='发票总数',
                      yaxis2_title="ocr引用率",
                      barmode='group')
    st.plotly_chart(fig, key='st_plot_2')

    sub_text_write('初审人员发票OCR整体引用情况')
    df_ocr_invoice_person = get_ocr_invoice_person(product_serial_code, start_time, end_time)
    st.dataframe(df_ocr_invoice_person, hide_index=True, use_container_width=True)
    sub_text_write('初审人员发票OCR引用分布')
    # 创建一个带有子图的图表
    fig = make_subplots(specs=[[{"secondary_y": True}]])
    # 筛选产品系列
    for product in product_serial_name:
        df_filtered = df_ocr_invoice_person[df_ocr_invoice_person['产品系列'] == product]
        fig.add_trace(
            go.Bar(x=df_filtered['姓名'].tolist(), y=df_filtered['总数'].tolist(),
                   name=f'{product}发票总数', text=df_filtered['总数'].tolist()), secondary_y=False)

        # 添加赔付人数的折线图系列
        fig.add_trace(
            go.Scatter(x=df_filtered['姓名'].tolist(), y=df_filtered['OCR引用率'].tolist(),
                       name=f'{product}审核人员OCR引用率', mode='lines+markers', yaxis='y2'),
            secondary_y=True
        )
        fig.add_trace(
            go.Scatter(x=df_filtered['姓名'].tolist(), y=df_filtered['保司驳回率'].tolist(),
                       name=f'{product}保司驳回率', mode='lines+markers', yaxis='y2'),
            secondary_y=True
        )
    # 移除副y轴的网格线
    fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
    # 设置y轴和y次轴的范围模式为从0开始
    fig.update_yaxes(rangemode="tozero", secondary_y=False)
    fig.update_yaxes(rangemode="tozero", secondary_y=True)

    fig.update_layout(title='',
                      xaxis_title='',
                      yaxis_title='发票总数',
                      yaxis2_title="ocr引用率、驳回率",
                      barmode='group')
    st.plotly_chart(fig, key='st_plot_person_2')


def st_plot_3(product_serial_code, start_time, end_time, remove_zero, sample_type):
    """
    准确率对比
    :param product_serial_code: 产品系列code
    :param start_time: 数据开始日期
    :param end_time: 数据结束日期
    :param remove_zero: 是否剔除0值
    :return:
    """
    text_write('就诊准确率')
    sub_text_write('就诊准确率整体情况')
    df_ocr_medical_accuracy_info, df_transformed_daily = get_ocr_medical_accuracy_info(
        product_serial_code, start_time, end_time, remove_zero, sample_type)
    if df_ocr_medical_accuracy_info.empty:
        df_product = get_product_code()
        df_product_serial_map= df_product[['product_serial_code', 'product_serial_name']]
        product_serial_name = df_product_serial_map[df_product_serial_map['product_serial_code'] == product_serial_code][
            'product_serial_name'].values[0]
        st.info(f'{product_serial_name}暂无数据')
        return
    st.dataframe(df_ocr_medical_accuracy_info, hide_index=True, use_container_width=True)
    sub_text_write('就诊准确率日度情况')
    st.dataframe(df_transformed_daily, hide_index=True, use_container_width=True)
    name = '就诊ocr对比明细.xlsx'
    path_name = Path.cwd().joinpath('temp_files').joinpath(name)

    download_file(excel_name=path_name, file_name=name, label='下载对比明细表格')
    product_list = df_transformed_daily.产品系列.unique().tolist()
    sub_text_write('就诊准确率趋势图')
    with st.spinner('图表绘制中...'):
        for product in product_list:
            # 创建一个带有子图的图表
            fig = make_subplots(specs=[[{"secondary_y": True}]])
            fig2 = make_subplots(specs=[[{"secondary_y": True}]])
            df_filtered = df_transformed_daily[df_transformed_daily['产品系列'] == product]
            df_filtered = df_filtered[df_filtered['字段'] != 'is_same']
            keys = df_filtered['字段'].unique().tolist()
            for key in keys:
                df_filtered_key = df_filtered[df_filtered['字段'] == key]
                df_filtered_key = df_filtered_key.reset_index(drop=True)
                # 筛选产品系列
                fig.add_trace(
                    go.Scatter(x=df_filtered_key['日期'].tolist(), y=df_filtered_key['准确率'].tolist(),
                               name=f'{key}准确率', mode='lines+markers', yaxis='y2'), secondary_y=False)
                # 计算OCR引用率的趋势线
                x = np.arange(len(df_filtered_key))
                y = df_filtered_key['准确率'].tolist()

                if len(x) < 2 or len(y) < 2:
                    z = None
                    p = None
                else:
                    try:
                        z = np.polyfit(x, y, 1)  # 计算一次多项式拟合，即线性回归
                        p = np.poly1d(z)  # 创建多项式函数
                    except np.linalg.LinAlgError as e:
                        print("拟合失败:", e)
                        z = None
                        p = None

                if p is not None:
                    # 添加趋势线
                    fig2.add_trace(
                        go.Scatter(x=df_filtered_key['日期'].tolist(), y=p(x),
                                   name=f'{key}准确率趋势线', mode='lines',
                                   yaxis='y2'),
                        secondary_y=False
                    )
            fig.update_xaxes(tickformat="%Y-%m-%d")
            # 移除副y轴的网格线
            fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
            # 设置y轴和y次轴的范围模式为从0开始
            fig.update_yaxes(rangemode="tozero", secondary_y=False)
            fig.update_yaxes(rangemode="tozero", secondary_y=True)

            fig.update_layout(title=f'{product}准确率',
                              xaxis_title='日期',
                              yaxis_title='准确率',
                              barmode='group')
            st.plotly_chart(fig, key=f'st_plot_3_{product}_{key}')

            fig2.update_xaxes(tickformat="%Y-%m-%d")
            # 移除副y轴的网格线
            fig2.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
            # 设置y轴和y次轴的范围模式为从0开始
            fig2.update_yaxes(rangemode="tozero", secondary_y=False)
            fig2.update_yaxes(rangemode="tozero", secondary_y=True)

            fig2.update_layout(title=f'{product}准确率趋势图',
                               xaxis_title='',
                               yaxis_title='',
                               barmode='group')
            st.plotly_chart(fig2, key=f'st_plot_4_{product}_{key}')


def st_plot_4(product_serial_code, product_serial_code_list, start_time, end_time, sample_type):
    """
    发票准确率对比
    :param product_serial_code: 产品系列code
    :param start_time: 数据开始日期
    :param end_time: 数据结束日期
    :param remove_zero: 是否剔除0值
    :return:
    """

    text_write('发票准确率')
    text_new("准确率:包括0值、空值；准确率V1：录入数据值大于0")
    sub_text_write('发票准确率整体情况')
    df_ocr_invoice_detail = get_ocr_invoice_detail(product_serial_code, start_time, end_time)
    df = get_ocr_invoice_accuracy_info(df_ocr_invoice_detail, product_serial_code_list)
    if df.empty:
        df_product = get_product_code()
        df_product_serial_map= df_product[['product_serial_code', 'product_serial_name']]
        product_serial_name = df_product_serial_map[df_product_serial_map['product_serial_code'] == product_serial_code][
            'product_serial_name'].values[0]
        st.info(f'{product_serial_name}暂无数据')
        return
    st.dataframe(df, hide_index=True, use_container_width=True)
    sub_text_write('发票准确率日度情况')
    df_daily = get_ocr_invoice_accuracy_info_daily(df_ocr_invoice_detail, product_serial_code_list, sample_type)
    st.dataframe(df_daily, hide_index=True, use_container_width=True)

    name = '发票ocr对比明细.xlsx'
    path_name = Path.cwd().joinpath('temp_files').joinpath(name)

    download_file(excel_name=path_name, file_name=name, label='下载对比明细表格')

    product_list = df_daily.产品系列.unique().tolist()
    sub_text_write('就诊准确率趋势图')
    with st.spinner('图表绘制中...'):
        for product in product_list:
            for accuracy in ['准确率','准确率V1']:
                # 创建一个带有子图的图表
                fig = make_subplots(specs=[[{"secondary_y": True}]])
                fig2 = make_subplots(specs=[[{"secondary_y": True}]])
                df_filtered = df_daily[df_daily['产品系列'] == product]
                df_filtered = df_filtered[df_filtered['字段'] != 'is_same']
                keys = df_filtered['字段'].unique().tolist()
                for key in keys:
                    df_filtered_key = df_filtered[df_filtered['字段'] == key]
                    df_filtered_key = df_filtered_key.reset_index(drop=True)
                    # 筛选产品系列
                    fig.add_trace(
                        go.Scatter(x=df_filtered_key['日期'].tolist(),
                                   y=df_filtered_key[accuracy].tolist(),
                                   name=f'{key}{accuracy}', mode='lines+markers', yaxis='y2'), secondary_y=False)
                    # 计算OCR引用率的趋势线
                    x = np.arange(len(df_filtered_key))
                    y = df_filtered_key[accuracy].tolist()

                    if len(x) < 2 or len(y) < 2:
                        z = None
                        p = None
                    else:
                        try:
                            z = np.polyfit(x, y, 1)  # 计算一次多项式拟合，即线性回归
                            p = np.poly1d(z)  # 创建多项式函数
                        except np.linalg.LinAlgError as e:
                            print("拟合失败:", e)
                            z = None
                            p = None

                    if p is not None:
                        # 添加趋势线
                        fig2.add_trace(
                            go.Scatter(x=df_filtered_key['日期'].tolist(), y=p(x),
                                       name=f'{key}{accuracy}趋势线', mode='lines',
                                       yaxis='y2'),
                            secondary_y=False
                        )
                fig.update_xaxes(tickformat="%Y-%m-%d")
                # 移除副y轴的网格线
                fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
                # 设置y轴和y次轴的范围模式为从0开始
                fig.update_yaxes(rangemode="tozero", secondary_y=False)
                fig.update_yaxes(rangemode="tozero", secondary_y=True)

                fig.update_layout(title=f'{product}{accuracy}',
                                  xaxis_title='',
                                  yaxis_title=f'{accuracy}',
                                  barmode='group')
                st.plotly_chart(fig, key=f'st_plot_6_{product}_{accuracy}_{key}')

                fig2.update_xaxes(tickformat="%Y-%m-%d")
                # 移除副y轴的网格线
                fig2.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
                # 设置y轴和y次轴的范围模式为从0开始
                fig2.update_yaxes(rangemode="tozero", secondary_y=False)
                fig2.update_yaxes(rangemode="tozero", secondary_y=True)

                fig2.update_layout(title=f'{product}{accuracy}趋势图',
                                   xaxis_title='',
                                   yaxis_title='',
                                   barmode='group')
                st.plotly_chart(fig2, key=f'st_plot_5_{product}_{accuracy}_{key}')


@st.fragment
def download_file(excel_name, file_name, label='下载数据'):
    """
    下载文件
    :param file_path: 文件路径
    :param file_name: 文件名称
    :return:
    """
    st.download_button(
        label=label,
        data=open(excel_name, 'rb').read(),
        file_name=file_name,
        mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )


def main():
    # 权限检查
    is_iframe, user_permission, disable, product_serial_code_iframe, product_set_code_iframe = agent_auth_check()
    st.subheader("OCR识别报告")
    product_info = get_product_code()
    if product_serial_code_iframe in product_info['product_serial_code'].values or product_serial_code_iframe is None:
        if is_iframe == 1 and product_serial_code_iframe is not None:
            iframe_product_serial_name = \
            product_info[product_info['product_serial_code'] == product_serial_code_iframe][
                'product_serial_name'].values[0]
            product_serial_name = st.multiselect('产品系列', product_info['product_serial_name'].tolist(),
                                                 default=[iframe_product_serial_name],disabled=True)
        else:
            product_serial_name = st.multiselect('产品系列', product_info['product_serial_name'].tolist(),
                                                 default=['江苏医惠保'])

        cols = st.columns([0.4, 0.4, 0.2, 0.2])
        # 选择日期
        sale_from = datetime.datetime.now() - datetime.timedelta(days=7)
        sale_until = datetime.date.today()

        with cols[0]:
            start_date = st.date_input('开始日期', min_value='2000-01-01', max_value=sale_until, value=sale_from,
                                       key='start_date')

        with cols[1]:
            end_date = st.date_input('结束日期', min_value='2000-01-01', max_value=sale_until, value=sale_until,
                                     key='end_date')
        with cols[2]:
            remove_zero = st.selectbox('剔除0值', ['是', '否'], key='zero_value')
            if remove_zero == '是':
                remove_zero = True
            else:
                remove_zero = False

        with cols[3]:
            sample_type = st.selectbox('频度', ['日', '周', '月'])

        if start_date > end_date:
            st.error('开始日期不能大于结束日期')
        else:
            if start_date:
                start_time = start_date.strftime('%Y-%m-%d 00:00:00')
            else:
                start_time = datetime.date(2021, 1, 7).strftime('%Y-%m-%d 00:00:00')
            if end_date:
                end_time = end_date.strftime('%Y-%m-%d 23:59:59')
            else:
                end_time = datetime.date.today().strftime('%Y-%m-%d 23:59:59')
            if product_serial_name:
                product_info_input = product_info[product_info['product_serial_name'].isin(product_serial_name)]
                product_serial_code = "','".join(product_info_input['product_serial_code'].tolist())
                product_serial_code_list = product_info_input['product_serial_code'].tolist()
            else:
                product_serial_code = "','".join(product_info['product_serial_code'].tolist())
                product_serial_name = product_info['product_serial_name'].tolist()
                product_serial_code_list = product_info['product_serial_code'].tolist()
            st.divider()
            tabs = st.tabs(['引用率', '准确率(就诊字段)', '准确率(发票字段)'])
            with tabs[0]:
                with st.spinner('查询中...'):
                    st_plot_1(product_serial_code, product_serial_name, start_time, end_time, remove_zero, sample_type)
                    # st_plot_+2(product_serial_code, product_serial_name, start_time, end_time, remove_zero, sample_type)
            with tabs[1]:
                with st.spinner('查询中，耗时较长，请稍后...'):
                    st_plot_3(product_serial_code, start_time, end_time, remove_zero, sample_type)
            with tabs[2]:
                with st.spinner('查询中，耗时较长，请稍后...'):
                    st_plot_4(product_serial_code, product_serial_code_list, start_time, end_time, sample_type)
    else:
        st.info('未找到产品信息')


if __name__ == '__main__':
    main()
