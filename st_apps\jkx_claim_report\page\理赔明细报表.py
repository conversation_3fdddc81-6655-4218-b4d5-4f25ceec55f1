import datetime
import warnings
from pathlib import Path

import numpy as np
import pandas as pd
import streamlit as st  
from dateutil.relativedelta import relativedelta
from pandasql import sqldf
from st_aggrid import AgGrid

from utils.auth import agent_auth_check,auth_from_session
from utils.st import query_sql, empty_line
from utils.utils import replace_using_dict, sum_or_combine, round_values

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)



CONNECTOR_CLAIM = st.connection('claim', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品代码信息
    :return:
    """

    SQL_PRODUCT_CODE = '''
    SELECT 
        ps.NAME product_set_name,
        ps.CODE product_set_code,
        GROUP_CONCAT( p.CODE SEPARATOR ',' ) product_code,
        pss.code product_serial_code,
        valid_from
    FROM
        product p
        JOIN product_set ps ON p.product_set_code = ps.
        CODE JOIN product_serial pss ON pss.`code` = ps.product_serial_code 
    WHERE
        pss.CODE IN ( 'neimenggu_hmb', 'xiantao_xhb', 'shanxi_jkb', 'hunan_amb' ) 
    GROUP BY
        ps.NAME 
    ORDER BY
        valid_from DESC
    '''


    df_product_code = CONNECTOR_CLAIM.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    df_product_code['product_code_list'] = df_product_code['product_code'].apply(lambda x: x.split(','))
    # 将product_code中的字符串用单引号包裹
    df_product_code['product_code'] = df_product_code['product_code_list'].apply(
        lambda x: ','.join([f"'{i}'" for i in x]))
    df_product_code.drop(columns=['product_code_list'], inplace=True)
   
    df_product_code['product_code_shift'] = df_product_code.groupby('product_serial_code')['product_code'].shift(-1)
    df_product_code['product_name_shift'] = df_product_code.groupby('product_serial_code')['product_set_name'].shift(-1)
    return df_product_code


@st.cache_resource(ttl=3600)
def get_dict():
    """
    获取字典数据
    """
    # 获取字典数据，用于参数对应
    df_dict = CONNECTOR_DW.query("""select d.id,d.name,v.key,v.label from system_dict d join system_dict_value v 
                                        on d.id = v.dict_id and v.`status` =1
                                        where d.id in (4,6)
                                        """, show_spinner='数据获取中...', ttl=600)
    return df_dict


@st.cache_resource(ttl=600,show_spinner='数据获取中...')
def get_med_claim_detail(product_code, start_datetime=None, end_datetime=None,status=None,_conn=CONNECTOR_CLAIM):
    """
    理赔案件明细报表-发票明细与内蒙古一样，不再单独处理
    :param product_code:产品code
    :param end_date:结束日期
    :param sale_amount: 销售金额
    :param product_name:产品名称
    :param conn:数据库连接
    :return:
    """
    # 获取数据
    df_med_claim_detail = _conn.query(
        query_sql('SQL_MED_CLAIM_DETAIL_YX').format(product_code=product_code,start_datetime=start_datetime,
        end_datetime=end_datetime), show_spinner='数据获取中...',
        ttl=10)
    df_med_claim_detail['理赔id'] = df_med_claim_detail['理赔id'].astype(str)
    if status:
        df_med_claim_detail = df_med_claim_detail[df_med_claim_detail['案件状态'].isin(status)]
    # 数值列赋值空值为0，字符串列赋值空值为''
    for column in df_med_claim_detail.columns:
        if np.issubdtype(df_med_claim_detail[column].dtype, np.number):  # 检查是否为数值类型
            df_med_claim_detail[column] = df_med_claim_detail[column].fillna(0)  # 数值列中的空值替换为0
        # 如果是日期类型，改成2025-01-01 00:00:00的格式，如果为空不要显示NaT
        elif np.issubdtype(df_med_claim_detail[column].dtype, np.datetime64):
            df_med_claim_detail[column] = df_med_claim_detail[column].dt.strftime('%Y-%m-%d %H:%M:%S')
            df_med_claim_detail[column] = df_med_claim_detail[column].fillna('')
        else:
            df_med_claim_detail[column] = df_med_claim_detail[column].fillna('')  # 字符串列中的空值替换为''
    return df_med_claim_detail



def main():
    # 权限检查
    is_iframe, user_permission, disable, product_serial_code_iframe, product_set_code_iframe = agent_auth_check()
    person_info = auth_from_session()
    df_dict = get_dict()
    st.subheader("理赔明细报表")
    product_info = get_product_code()
    if product_set_code_iframe in product_info['product_set_code'].values or product_set_code_iframe is None:
        if is_iframe == 1 and product_set_code_iframe is not None:
            iframe_product_set_name = \
            product_info[product_info['product_set_code'] == product_set_code_iframe][
                'product_set_code'].values[0]
            product_set_name = st.multiselect('产品集', product_info['product_set_name'].tolist(),
                                                        default=[iframe_product_set_name],disabled=True)[0]
        
        # 限制圆心账号的查询范围，防止后续放开产品集的取数范围，不限制导致后续有问题
        elif person_info.get('displayName') in ['yx001','yx002']:
            # 限制product_serial_code 在'neimenggu_hmb', 'xiantao_xhb', 'shanxi_jkb', 'hunan_amb'中
            product_info_v1 = product_info[product_info['product_serial_code'].isin(['neimenggu_hmb', 'xiantao_xhb','shanxi_jkb', 'hunan_amb'])]
            product_set_name = st.multiselect('产品集', product_info_v1['product_set_name'].tolist(),
                                                        default=product_info_v1['product_set_name'].tolist())

        else:
            product_set_name = st.multiselect('产品集', product_info['product_set_name'].tolist(),
                                                 default=product_info['product_set_name'].tolist())
        if product_set_name:
            product_info_input = product_info[product_info['product_set_name'].isin(product_set_name)]
            product_set_code = "','".join(product_info_input['product_set_code'].tolist())
            product_set_name = product_info_input['product_set_name'].tolist()
            product_set_code_list = product_info_input['product_set_code'].tolist()
            product_code = ",".join(product_info_input['product_code'].tolist())
        else:
            product_set_code = "','".join(product_info['product_set_code'].tolist())
            product_set_name = product_info['product_set_name'].tolist()
            product_set_code_list = product_info['product_set_code'].tolist()
            product_code = ",".join(product_info['product_code'].tolist())
        # 过滤掉 NOT_SUBMITTED 和 CANCELED 状态
        available_statuses = df_dict[(df_dict['id'] == 4) & (~df_dict['label'].isin(['NOT_SUBMITTED', 'CANCELED']))]['key'].tolist()
        status = st.multiselect('状态', available_statuses, key='type',
                                default=available_statuses)
        if status is None:
            status = available_statuses
        sale_from = datetime.date(2021, 1, 1)
        start_from = datetime.date.today() - relativedelta(months=1)
        sale_until = datetime.date.today()
        cols = st.columns(2)
        with cols[0]:
            start_date = st.date_input('申请开始日期', min_value=sale_from, max_value=sale_until, value=start_from)
        with cols[1]:
            end_date = st.date_input('申请结束日期', min_value=sale_from, max_value=sale_until, value=sale_until)

        if start_date is None:
            start_date = sale_from
        if end_date is None:
            end_date = sale_until
        
        st.divider()
        if start_date and end_date and start_date > end_date:
            st.error('开始日期不能大于结束日期')
        else:
            # 开始时间为2021-01-01 00:00:00这种格式
            start_datetime = datetime.datetime.combine(start_date, datetime.time.min)
            # 结束时间为2021-12-31 23:59:59这种格式，不要毫秒
            end_datetime = datetime.datetime.combine(end_date, datetime.time(23, 59, 59))

            df_med_claim_detail = get_med_claim_detail(product_code,  start_datetime, end_datetime,status)

            number_sum = df_med_claim_detail.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
            # number_sum 除第一列的合计，其他合计都转成None
            number_sum.replace('合计', None, inplace=True)
            number_sum.iloc[0]['序号'] = '合计'
            # 下载数据
            excel_name = Path.cwd().joinpath('temp_files').joinpath("理赔案件明细报表.xlsx")
            df_med_claim_detail.to_excel(excel_name, index=False)
            empty_line(1)
            st.download_button(
                label='下载数据',
                data=open(excel_name, 'rb').read(),
                file_name='理赔明细报表.xlsx',
                mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            # 定义AgGrid的参数
            aggrid_options = {
                'columnDefs': [{'headerName': c, 'field': c} for c in df_med_claim_detail.columns],
                'pagination': True,  # 启用分页
                'paginationPageSize': 10,  # 每页显示的记录数
                'autoHeight': True,  # 自适应高度
                'autoSizeStrategy': {
                    'type': 'fitCellContents'
                },
                'defaultColDef': {
                    'filter': True
                },
                'pinnedBottomRowData': [round_values(record) for record in number_sum.to_dict('records')],
                # 设置底部合计行，保留2位小数，dict会精度丢失
            }
            # 使用st_aggrid显示分页表格
            AgGrid(df_med_claim_detail, gridOptions=aggrid_options, theme='streamlit')
    else:
        st.info('该产品无该理赔报表')

if __name__ == '__main__':
    main()
