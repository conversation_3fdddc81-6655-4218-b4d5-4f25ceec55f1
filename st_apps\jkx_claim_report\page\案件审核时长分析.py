import datetime
import json
import warnings
from pathlib import Path
import jwt
import numpy as np
import pandas as pd
import streamlit as st
from openpyxl.styles import PatternFill
from openpyxl import Workbook, load_workbook
from streamlit.runtime.secrets import secrets_singleton
from urllib.parse import quote
import streamlit_antd_components as sac
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from pyecharts import options as opts
from pyecharts.charts import Pie
from streamlit_echarts import st_pyecharts
from pandasql import sqldf
from st_aggrid import JsCode, AgGrid, GridOptionsBuilder, GridUpdateMode
from utils.auth import auth_from_session, get_agent_auth, JWT_SECRET, agent_auth_check
from utils.utils import send_feishu_message, replace_using_dict
from utils.st import query_sql, text_write, empty_line, sub_text_write, text_new
from utils.date import get_shifted_dates
import clickhouse_connect

warnings.filterwarnings("ignore")
pd.set_option("display.max_columns", None)
pd.set_option("display.expand_frame_repr", False)
pd.set_option("display.float_format", lambda x: "%.2f" % x)

total_connection_info = secrets_singleton["connections"]
ck_conn = total_connection_info["tracker"]

CONNECTOR = st.connection("claim", type="sql")
CONNECTOR_DW = st.connection("dw", type="sql")
CONNECTOR_NHB = st.connection("claim_nhb", type="sql")
CONNECTOR_GHB = st.connection("claim_guizhou", type="sql")
CONNECTOR_YZ = st.connection("claim_yangzi", type="sql")



def get_product_set_code():
    """
    获取产品集编码
    :return:
    """
    SQL_PRODUCT_SET_CODE = """
    SELECT CODE
        product_set_code,
        NAME product_set_name ,
        '南京宁惠保' product_serial_name,
        'ninghuibao' product_serial_code 
    FROM
        `product_set`
"""
    SQL_PRODUCT_SET_CODE_V1 = """
    SELECT
        ps.CODE product_set_code,
        ps.NAME product_set_name,
        pss.NAME product_serial_name,
        pss.CODE product_serial_code 
    FROM
        `product_set` ps
        JOIN product_serial pss ON ps.product_serial_code = pss.CODE
    """
    SQL_PRODUCT_SET_CODE_GZ = """
    SELECT 
        product_set_code,
    CASE
            WHEN NAME = '贵惠保' THEN
            '贵惠保一期' ELSE NAME 
        END product_set_name,
        '贵惠保' product_serial_name,
        'guihuibao' product_serial_code 
    FROM
        `product`
    group by product_set_code
    """
    SQL_PRODUCT_SET_CODE_YZ = """
    SELECT CODE
	product_set_code,
	NAME product_set_name,
	SUBSTRING_INDEX( SUBSTRING_INDEX( NAME, '企业', 1 ), '企业', - 1 ) product_serial_name,
	SUBSTRING_INDEX( SUBSTRING_INDEX( CODE, '-', 1 ), '-', - 1 ) product_serial_code 
FROM
	`product`
    """

    df = CONNECTOR.query(SQL_PRODUCT_SET_CODE_V1, show_spinner="查询中...", ttl=100)
    df_nhb = CONNECTOR_NHB.query(SQL_PRODUCT_SET_CODE, show_spinner="查询中...", ttl=100)
    df_ghb = CONNECTOR_GHB.query(SQL_PRODUCT_SET_CODE_GZ, show_spinner="查询中...", ttl=100)
    df_yz = CONNECTOR_YZ.query(SQL_PRODUCT_SET_CODE_YZ, show_spinner="查询中...", ttl=100)


    df_total = pd.concat([df, df_nhb], ignore_index=True)
    df_total = pd.concat([df_total, df_ghb], ignore_index=True)
    df_total = pd.concat([df_total, df_yz], ignore_index=True)
    # 剔除滨州、日照、德州的产品
    df_total = df_total[~df_total["product_set_name"].str.contains("滨州") & ~df_total["product_set_name"].str.contains("日照") & ~df_total["product_set_name"].str.contains("德州")]
    return df_total


def get_connection(DB):
    """
    获取数据库连接
    """
    client = clickhouse_connect.get_client(
        host=DB["host"],
        port=int(DB["port"]),
        username=DB["username"],
        password=DB["password"],
        database=DB["database"],
    )
    return client


def get_tracking_data(product_set_code,start_time,end_time):
    """
    获取tracking_data数据
    :param product_code: 产品code
    :param start_date: 数据开始日期
    :param end_date: 数据结束日期
    :return:
    """
    SQL_TRACKING_DATA = f"""
    SELECT * from tracking_focus 
    where create_time >= '{start_time}' 
    and create_time <= '{end_time}'
    and product_set_code in ('{product_set_code}')
    """
    with get_connection(ck_conn) as conn:
        df_tracking_data = conn.query_df(SQL_TRACKING_DATA)
    if not df_tracking_data.empty:
        df_tracking_data["event_type"] = df_tracking_data["event_type"].apply(
            lambda x: replace_using_dict(
                x, {"child-active": "active", "child-hidden": "hidden", "child-closed": "closed",
                "focus":'active',"blur":'hidden'}
            )
        )
        df_tracking_data["user_id"] = df_tracking_data["user_id"].astype(str)
    else:
        df_tracking_data = pd.DataFrame(columns=["event_type", "user_id", "create_time", "product_set_code","source_id"])
    return df_tracking_data


def get_audit():
    """
    获取审核人员id
    :return:
    """
    SQL_AUDIT = f"""
    SELECT DISTINCT
	u.id user_id 
FROM
	`user` u
	JOIN user_role ur ON u.id = ur.user_id
	JOIN `role` r ON ur.role_id = r.id 
WHERE
	r.NAME IN (
	'初审人员',
	'复审人员')
    """
    df = CONNECTOR.query(SQL_AUDIT, show_spinner="查询中...", ttl=10)
    df_nhb = CONNECTOR_NHB.query(SQL_AUDIT, show_spinner="查询中...", ttl=10)
    df_ghb = CONNECTOR_GHB.query(SQL_AUDIT, show_spinner="查询中...", ttl=10)
    df_yz = CONNECTOR_YZ.query(SQL_AUDIT, show_spinner="查询中...", ttl=10)
    df = pd.concat([df, df_ghb], ignore_index=True)
    df = pd.concat([df, df_yz], ignore_index=True)
    df = pd.concat([df, df_nhb], ignore_index=True)
    df["user_id"] = df["user_id"].astype(str)
    return df



def get_audit_case(product_set_code,start_time=None,end_time=None):
    """
    获取审核的案件
    :param start_time:开始时间
    :param end_time:结束时间
    :param sql:查询sql
    :return:
    """
    if start_time is None:
        start_time = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y-%m-%d 00:00:00")
    if end_time is None:
        end_time = datetime.datetime.now().strftime("%Y-%m-%d 23:59:59")
    SQL_YHB = f"""
        SELECT
        ice.claim_id,
        ic.product_set_code,
        GROUP_CONCAT(  ice.STATUS order by ice.create_time,ice.`status` desc  ) group_status 
        ,date(min(CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ))) create_date
    FROM
        insurance_claim_event ice
        JOIN insurance_claim ic ON ice.claim_id = ic.id 
    WHERE
        CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ) >= '{start_time}' 
        AND CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ) <= '{end_time}' 
        AND ice.STATUS IN ( 'CLAIM_APPLICATION_AUDITED', 'SYSTEM_REJECTED', 'SYSTEM_AUDITED', 'INSURANCE_COMPANY_ACCEPTED', 'WAIT_REVIEW' ) 
        AND ic.product_set_code IN ( '{product_set_code}' ) 
    GROUP BY
        ice.claim_id
        """
    SQL = f"""
    SELECT
	ice.claim_id,
    ic.product_set_code,
	GROUP_CONCAT(  ice.STATUS order by ice.create_time,ice.`status` desc  ) group_status ,
	date(min(CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ))) create_date
FROM
	insurance_claim_event ice
	JOIN insurance_claim ic ON ice.claim_id = ic.id 
WHERE
	CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ) >= '{start_time}' 
    AND CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ) <= '{end_time}' 
	AND ice.STATUS IN ( 'CLAIM_APPLICATION_AUDITED', 'WAIT_INSURANCE_COMPANY_AUDITED','INSURANCE_COMPANY_ACCEPTED', 'SYSTEM_REJECTED', 'SYSTEM_AUDITED' ) 
	AND ic.product_set_code IN ( '{product_set_code}' ) 
GROUP BY
	ice.claim_id
    """
    SQL_GZ = f"""
    SELECT
        ice.claim_id,
        p.product_set_code,
        GROUP_CONCAT(  ice.STATUS order by ice.create_time,ice.`status` desc  ) group_status ,
        date(min(CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ))) create_date
    FROM
        insurance_claim_event ice
        JOIN insurance_claim ic ON ice.claim_id = ic.id 
        join product p on ic.product_code = p.code
    WHERE
        CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ) >= '{start_time}' 
        AND CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ) <= '{end_time}' 
        AND ice.STATUS IN ( 'CLAIM_APPLICATION_AUDITED', 'WAIT_INSURANCE_COMPANY_AUDITED','INSURANCE_COMPANY_ACCEPTED', 'SYSTEM_REJECTED', 'SYSTEM_AUDITED' ) 
        AND p.product_set_code IN ( '{product_set_code}' ) 
    GROUP BY
        ice.claim_id
    """
    SQL_YZ = f"""
    SELECT
        ice.claim_id,
        ic.product_code product_set_code,
        GROUP_CONCAT(  ice.STATUS order by ice.create_time,ice.`status` desc  ) group_status ,
        date(min(CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ))) create_date
    FROM
        insurance_claim_event ice
        JOIN insurance_claim ic ON ice.claim_id = ic.id 
    WHERE
        CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ) >= '{start_time}' 
        AND CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ) <= '{end_time}' 
        AND ice.STATUS IN ( 'CLAIM_APPLICATION_AUDITED', 'WAIT_INSURANCE_COMPANY_AUDITED','INSURANCE_COMPANY_ACCEPTED', 'SYSTEM_REJECTED', 'SYSTEM_AUDITED' ) 
        AND ic.product_code IN ( '{product_set_code}' ) 
    GROUP BY
        ice.claim_id
    """
    
    df_yhb = CONNECTOR.query(SQL_YHB, show_spinner="查询中...", ttl=10)
    df_other = CONNECTOR.query(SQL, show_spinner="查询中...", ttl=10)
    df_nhb = CONNECTOR_NHB.query(SQL, show_spinner="查询中...", ttl=10)
    df_ghb = CONNECTOR_GHB.query(SQL_GZ, show_spinner="查询中...", ttl=10)
    df_yz = CONNECTOR_YZ.query(SQL_YZ, show_spinner="查询中...", ttl=10)
    # df_yhb筛选product_set_code 以jiangsu_yhb开头的数据，同时数据在传入的参数product_set_code中，需要先将product_set_code转为list
    
    product_set_code_list = product_set_code.replace("'","")
    product_set_code_list = product_set_code_list.split(',')
    # product_set_code_list 以jiangsu_yhb开头的数据
    yhb_product_set_code_list = [code for code in product_set_code_list if code.startswith('jiangsu_yhb')]
    df_yhb_filtered = df_yhb[df_yhb['product_set_code'].isin(yhb_product_set_code_list)]

    other_product_set_code_list = [code for code in product_set_code_list if not code.startswith('jiangsu_yhb')]
    df_other_filtered = df_other[df_other['product_set_code'].isin(other_product_set_code_list)]

    nhb_product_set_code_list = [code for code in product_set_code_list if code.startswith('ninghuibao')]
    df_nhb_filtered = df_nhb[df_nhb['product_set_code'].isin(nhb_product_set_code_list)]

    ghb_product_set_code_list = [code for code in product_set_code_list if code.startswith('guihuibao')]
    df_ghb_filtered = df_ghb[df_ghb['product_set_code'].isin(ghb_product_set_code_list)]

    # 定义需要包含的状态组合
    required_combinations = [
        {'CLAIM_APPLICATION_AUDITED', 'INSURANCE_COMPANY_ACCEPTED'},
        {'CLAIM_APPLICATION_AUDITED', 'WAIT_REVIEW'},
        {'CLAIM_APPLICATION_AUDITED', 'SYSTEM_REJECTED'}
    ]

    other_required_combinations = [
        {'CLAIM_APPLICATION_AUDITED', 'WAIT_INSURANCE_COMPANY_AUDITED'},
        {'CLAIM_APPLICATION_AUDITED', 'INSURANCE_COMPANY_ACCEPTED'},
        {'CLAIM_APPLICATION_AUDITED', 'SYSTEM_AUDITED'},
        {'CLAIM_APPLICATION_AUDITED', 'SYSTEM_REJECTED'}
    ]

    # 筛选包含任意一个状态组合的记录
    df_yhb_filtered = df_yhb_filtered[
        df_yhb_filtered['group_status'].apply(
            lambda x: any(
                combo.issubset(set(x.split(','))) 
                for combo in required_combinations
            )
        )
    ]

    df_other_filtered = df_other_filtered[
        df_other_filtered['group_status'].apply(
            lambda x: any(
                combo.issubset(set(x.split(','))) 
                for combo in other_required_combinations
            )
        )
    ]

    df_nhb_filtered = df_nhb_filtered[
        df_nhb_filtered['group_status'].apply(
            lambda x: any(
                combo.issubset(set(x.split(',')))
                for combo in other_required_combinations
            )
        )
    ]

    df_ghb_filtered = df_ghb_filtered[
        df_ghb_filtered['group_status'].apply(
            lambda x: any(
                combo.issubset(set(x.split(',')))
                for combo in other_required_combinations
            )
        )
    ]

    df_yz_filtered = df_yz[
        df_yz['group_status'].apply(
            lambda x: any(
                combo.issubset(set(x.split(',')))
                for combo in other_required_combinations
            )
        )
    ]

    df = pd.concat([df_yhb_filtered,df_other_filtered],ignore_index=True)
    df = pd.concat([df,df_ghb_filtered],ignore_index=True)
    df = pd.concat([df,df_yz_filtered],ignore_index=True)
    df = pd.concat([df,df_nhb_filtered],ignore_index=True)
    df.reset_index(drop=True,inplace=True)
    if not df.empty:
        df['claim_id'] = df['claim_id'].astype(str)
        df.rename(columns = {'claim_id':'source_id'},inplace = True)
        df.drop(columns = ['product_set_code'],inplace = True)
    else:
        df = pd.DataFrame(columns = ['source_id','group_status','create_date'])
    return df



def get_case_statis(product_set_code, start_time=None, end_time=None):
    """
    获取案件审核时长统
    :param start_time:开始时间
    :param end_time:结束时间
    :return:
    """
    df_tracking_data = get_tracking_data(product_set_code,start_time, end_time)
    df_product_set_code = get_product_set_code()

    df_audit = get_audit()
    df_tracking_data = df_tracking_data.merge(df_audit, on="user_id", how="inner")
    

    # 根据source_id 、user_id 进行分组，只统计event_type 是active-active、active-hidden、active-closed的记录的create_time
    df_tracking_data = df_tracking_data.sort_values(by = ['user_id','source_id','create_time'])
    df_tracking_data['event_type_shift'] = df_tracking_data.groupby(['user_id', 'source_id'])[
        'event_type'].shift(1)
    df_tracking_data['create_time_shift'] = df_tracking_data.groupby(['user_id', 'source_id'])[
        'create_time'].shift(1)
    df_tracking_data['event_concat'] = df_tracking_data['event_type_shift'] + '-' + df_tracking_data['event_type']
    df_tracking_data = df_tracking_data[df_tracking_data['event_concat'].isin(['active-active','active-hidden','active-closed'])]
    # create_time 与 create_time_shift 要在同一天
    if not df_tracking_data.empty:
        df_tracking_data = df_tracking_data[
            df_tracking_data['create_time'].dt.date == df_tracking_data['create_time_shift'].dt.date
        ]
        df_tracking_data['duration'] = (df_tracking_data['create_time'] - df_tracking_data['create_time_shift']).dt.total_seconds()
        df_tracking_data['create_date'] = df_tracking_data['create_time'].dt.date
    else:
        # 如果DataFrame为空，返回一个包含所需列的空DataFrame
        df_tracking_data = pd.DataFrame(columns=['create_date', 'source_id', 'product_set_code', 'duration'])
    # 根据source_id 统计耗时
    df_claim_cost_time = df_tracking_data.groupby(['source_id','product_set_code'])['duration'].sum().reset_index()
    df_claim_cost_time.sort_values(by = ['duration'],ascending = False,inplace = True)
    df_claim_cost_time.reset_index(drop = True,inplace = True)
    df_today_audit_case = get_audit_case(product_set_code,start_time,end_time)
    df_today_cost_time = df_claim_cost_time.merge(df_today_audit_case,on = ['source_id'],how = 'inner')
    # 根据group_status列取其中字符串最后一个,后面的数据，存入一个新的列group_status_last
    df_today_cost_time['group_status_last'] = df_today_cost_time['group_status'].apply(lambda x: x.split(',')[-1])



    df_today_cost_time['group_status_last'] = df_today_cost_time['group_status_last'].apply(lambda x: replace_using_dict(x,
                                                    {'SYSTEM_REJECTED':'系统驳回',
                                                    'WAIT_INSURANCE_COMPANY_AUDITED':'理赔通过(有理赔)',
                                                    'INSURANCE_COMPANY_ACCEPTED':'理赔通过(有理赔)',
                                                    'SYSTEM_AUDITED':'理赔通过(无理赔)',
                                                    'WAIT_REVIEW':'审核完成(医惠保特有)'}))
    # 删除df_today_cost_time的group_status_last列的值不在系统驳回、理赔通过(有理赔)、理赔通过(无理赔)、审核完成(医惠保特有)中的数据
    df_today_cost_time = df_today_cost_time[df_today_cost_time['group_status_last'].isin(['系统驳回','理赔通过(有理赔)','理赔通过(无理赔)','审核完成(医惠保特有)'])]
    df_today_cost_time['duration'] = round(df_today_cost_time['duration']/60,2)
    # product_set_code根据df_product_set_code中对应的字段替换成product_set_name
    df_today_cost_time['product_set_code'] = df_today_cost_time['product_set_code'].apply(lambda x: df_product_set_code[df_product_set_code['product_set_code']==x]['product_set_name'].values[0])
    df_today_cost_time.rename(columns = {'duration':'审核时长(分钟)','create_date':'操作日期',
                                        'source_id':'案件id','product_set_code':'产品集编码',
                                        'group_status_last':'状态'},inplace = True)
    df_today_cost_time.drop(columns = ['group_status'],inplace = True)
    return df_today_cost_time


def query_tracking_data(source_id, user_name, start_time, end_time):
    """
    查询tracking_data数据
    :param source_id: 案件ID
    :param user_name: 操作人员
    :param start_time: 开始时间
    :param end_time: 结束时间
    :return: 包含操作轨迹的DataFrame
    """
    # 构建基础SQL
    SQL = """
    SELECT
        source_id as `案件`,
        user_name as `操作人员`, 
        event as `操作行为`,
        create_time as `操作时间`
    FROM tracking_data
    WHERE source_id != ''
    and position(lower(event_code), 'referral') = 0
    """
    
    # 动态添加过滤条件
    conditions = []
    if source_id:
        conditions.append(f"source_id = '{source_id}'")
    if user_name:
        conditions.append(f"user_name = '{user_name}'")
    if start_time:
        conditions.append(f"create_time >= '{start_time}'")
    if end_time:
        conditions.append(f"create_time <= '{end_time}'")
    
    # 拼接完整SQL
    if conditions:
        SQL += " AND " + " AND ".join(conditions)
    SQL += " ORDER BY user_name,source_id,create_time"
    # 执行查询
    with get_connection(ck_conn) as conn:
        df = conn.query_df(SQL)
    # 根据案件、操作人员分组，统计每一个步骤到下一个步骤的耗时
    if not df.empty:
        df['耗时(分钟)'] = np.where(
            df['操作时间'].dt.date == df.groupby(['案件', '操作人员'])['操作时间'].shift(1).dt.date,
            (df['操作时间'] - df.groupby(['案件', '操作人员'])['操作时间'].shift(1)).dt.total_seconds(),
            np.nan
        )
        df['耗时(分钟)'] = df['耗时(分钟)'].apply(lambda x: round(x / 60, 2) if pd.notna(x) else np.nan)
    else:
        df = pd.DataFrame()
    return df


@st.fragment
def tracking_data_layout():
    sale_from = datetime.datetime.now() - datetime.timedelta(days=7)
    sale_until = datetime.date.today()
    cols = st.columns(4)
    with cols[0]:
        source_id = st.text_input("理赔编码",key='case_id')
    with cols[1]:
        user_name = st.text_input("操作人员",key='operator_name')
    with cols[2]:
        start_date = st.date_input('开始日期', min_value='2000-01-01', max_value=sale_until, value=sale_from,
                                        key='start_date1')

    with cols[3]:
        end_date = st.date_input('结束日期', min_value='2000-01-01', max_value=sale_until, value=sale_until,
                                    key='end_date1')
    if start_date:
        start_time = start_date.strftime('%Y-%m-%d 00:00:00')
    if end_date:
        end_time = end_date.strftime('%Y-%m-%d 23:59:59')

    
    if st.button("查询"):
        if start_date > end_date:
            st.error('开始日期不能大于结束日期')
        elif not source_id and not user_name:
            st.error('请输入理赔编码或操作人员')
        else:
            with st.spinner('数据加载中...'):
                df_query_tracking_data = query_tracking_data(source_id,user_name,start_time,end_time)
                if df_query_tracking_data.empty:
                    st.info('未查询到数据')
                else:
                    text_new("耗时字段仅供参考，实际工作中可能会在差别大的时间中处理其他案件")
                    st.dataframe(df_query_tracking_data, use_container_width=True,hide_index=True)


def analyze_audit_duration(df,start_time,end_time):
    """
    对审核时长数据进行全面分析
    :param df: 审核时长数据DataFrame
    :return: 无
    """
    # 1. 基本统计量分析
    text_write("1. 基本统计量分析")
    duration_col = "审核时长(分钟)"
    
    # 计算基本统计量
    basic_stats = df[duration_col].describe()
    st.dataframe(basic_stats, use_container_width=True)
    
    # 5. 审核时长分布分析
    text_write("2. 审核时长区间分布")
    # 计算时长区间
    duration_ranges = [0, 5, 10, 15, 30, 60, float('inf')]
    labels = ['0-5分钟', '5-10分钟', '10-15分钟', '15-30分钟', '30-60分钟', '60分钟以上']
    df['时长区间'] = pd.cut(df[duration_col], bins=duration_ranges, labels=labels, right=False)
    duration_dist = df['时长区间'].value_counts().sort_index()
    
    # 计算占比和累计占比
    total_cases = duration_dist.sum()
    duration_pct = duration_dist / total_cases * 100
    cumulative_pct = duration_pct.cumsum()
    
    # 创建双Y轴图表
    fig = go.Figure()
    
    # 添加案件数量柱状图
    fig.add_trace(
        go.Bar(
            x=labels,
            y=duration_dist.values,
            name="案件数量",
            yaxis="y",
            text=duration_dist.values,
            textposition='outside'
        )
    )
    
    # 添加累计占比折线图
    fig.add_trace(
        go.Scatter(
            x=labels,
            y=cumulative_pct.values,
            name="累计占比",
            yaxis="y2",
            mode="lines+markers+text",
            line=dict(color="#ff7f0e", width=2),
            marker=dict(color="#ff7f0e", size=8),
            text=[f"{x:.1f}%" for x in cumulative_pct.values],
            textposition='top center',
            textfont=dict(color="#ff7f0e")
        )
    )
    
    # 更新布局设置双Y轴
    fig.update_layout(
        title="",
        yaxis=dict(
            title="案件数量", 
            titlefont=dict(color="#1f77b4"),
            gridcolor='lightgray',
            showgrid=True
        ),
        yaxis2=dict(
            title="累计占比(%)", 
            titlefont=dict(color="#ff7f0e"), 
            overlaying="y", 
            side="right",
            tickformat=".1f",
            range=[0, 110],  # 增加上限留出标签空间
            showgrid=False
        ),
        showlegend=True,
        bargap=0.2,
        plot_bgcolor='white'
    )
    
    st.plotly_chart(fig, use_container_width=True)


    # 3. 按状态分组分析
    text_write("3. 按状态分组分析")
    status_stats = df.groupby("状态")[duration_col].agg(["count", "mean", "median", "min", "max"]).reset_index()
    status_stats.columns = ["状态", "案件数量", "平均审核时长(分钟)", "中位数审核时长(分钟)", "最小审核时长(分钟)", "最大审核时长(分钟)"]
    status_stats = status_stats.sort_values(by="平均审核时长(分钟)", ascending=False)
    st.dataframe(status_stats, use_container_width=True,hide_index=True)
    
    # 绘制按状态分组的箱线图
    sub_text_write("不同状态的审核时长分布")
    fig_box = px.box(df, x="状态", y=duration_col, color="状态",
                    title="",
                    labels={duration_col: "审核时长(分钟)", "状态": "案件状态"})
    st.plotly_chart(fig_box, use_container_width=True)

    # 为每个状态创建审核时长区间分布图表
    for status in df["状态"].unique():
        sub_text_write(f"状态 '{status}' 的审核时长区间分布")
        df_status = df[df["状态"] == status]
        
        # 计算时长区间
        duration_ranges = [0, 5, 10, 15, 30, 60, float('inf')]
        labels = ['0-5分钟', '5-10分钟', '10-15分钟', '15-30分钟', '30-60分钟', '60分钟以上']
        df_status['时长区间'] = pd.cut(df_status[duration_col], bins=duration_ranges, labels=labels, right=False)
        duration_dist = df_status['时长区间'].value_counts().sort_index()
        
        # 计算占比和累计占比
        total_cases = duration_dist.sum()
        duration_pct = duration_dist / total_cases * 100
        cumulative_pct = duration_pct.cumsum()
        
        # 创建双Y轴图表
        fig = go.Figure()
        
        # 添加案件数量柱状图
        fig.add_trace(
            go.Bar(
                x=labels,
                y=duration_dist.values,
                name="案件数量",
                yaxis="y",
                text=duration_dist.values,
                textposition='outside'
            )
        )
        
        # 添加累计占比折线图
        fig.add_trace(
            go.Scatter(
                x=labels,
                y=cumulative_pct.values,
                name="累计占比",
                yaxis="y2",
                mode="lines+markers+text",
                line=dict(color="#ff7f0e", width=2),
                marker=dict(color="#ff7f0e", size=8),
                text=[f"{x:.1f}%" for x in cumulative_pct.values],
                textposition='top center',
                textfont=dict(color="#ff7f0e")
            )
        )
        
        # 更新布局设置双Y轴
        fig.update_layout(
            title="",
            yaxis=dict(
                title="案件数量", 
                titlefont=dict(color="#1f77b4"),
                gridcolor='lightgray',
                showgrid=True
            ),
            yaxis2=dict(
                title="累计占比(%)", 
                titlefont=dict(color="#ff7f0e"), 
                overlaying="y", 
                side="right",
                tickformat=".1f",
                range=[0, 110],
                showgrid=False
            ),
            showlegend=True,
            bargap=0.2,
            plot_bgcolor='white'
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    
    
    # 4. 按产品集编码分组分析
    text_write("4. 按产品集编码分组分析")
    if len(df["产品集编码"].unique()) > 1:
        product_stats = df.groupby("产品集编码")[duration_col].agg(["count", "mean", "median", "min", "max"]).reset_index()
        product_stats.columns = ["产品集编码", "案件数量", "平均审核时长(分钟)", "中位数审核时长(分钟)", "最小审核时长(分钟)", "最大审核时长(分钟)"]
        product_stats = product_stats.sort_values(by="平均审核时长(分钟)", ascending=False)
        st.dataframe(product_stats, use_container_width=True,hide_index=True)
        sub_text_write("不同产品集的平均审核时长和案件数量分布")
        # 创建双Y轴图表
        fig = go.Figure()
        
        # 添加平均审核时长柱状图
        fig.add_trace(
            go.Bar(
                x=product_stats["产品集编码"],
                y=product_stats["平均审核时长(分钟)"],
                name="平均审核时长",
                yaxis="y"
            )
        )
        
        # 添加案件数量折线图
        fig.add_trace(
            go.Scatter(
                x=product_stats["产品集编码"],
                y=product_stats["案件数量"],
                name="案件数量",
                yaxis="y2",
                mode="lines+markers",
                line=dict(color="#ff7f0e"),
                marker=dict(color="#ff7f0e")
            )
        )
        
        # 更新布局设置双Y轴
        fig.update_layout(
            title="",
            yaxis=dict(title="平均审核时长(分钟)", titlefont=dict(color="#1f77b4")),
            yaxis2=dict(
                title="案件数量", 
                titlefont=dict(color="#ff7f0e"), 
                overlaying="y", 
                side="right",
                showline=False,
                showgrid=False
            ),
            showlegend=True
        )
        
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.write("数据中只有一个产品集编码，无法进行产品集分组分析")
    

    
    # 5. 按操作日期分组分析
    text_write("5. 时间趋势分析")
    if len(df["操作日期"].unique()) > 1:
        date_stats = df.groupby("操作日期")[duration_col].agg(["count", "mean", "median", "min", "max"]).reset_index()
        date_stats.columns = ["操作日期", "案件数量", "平均审核时长(分钟)", "中位数审核时长(分钟)", "最小审核时长(分钟)", "最大审核时长(分钟)"]
        date_stats = date_stats.sort_values(by="操作日期")
        sub_text_write("整体趋势")
        st.dataframe(date_stats, use_container_width=True,hide_index=True)
        
        # 绘制时间趋势线图
        fig_line = px.line(date_stats, x="操作日期", y=["平均审核时长(分钟)", "中位数审核时长(分钟)"], 
                          title="审核时长变化趋势（包含异常值）",
                          labels={"value": "审核时长(分钟)", "操作日期": "日期", "variable": "统计量"})
        fig_line.update_xaxes(tickformat="%Y-%m-%d")
        st.plotly_chart(fig_line, use_container_width=True)
        
        # 剔除异常值后的时间趋势分析
        # 计算异常值的上下界
        Q1 = df[duration_col].quantile(0.25)
        Q3 = df[duration_col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = max(Q1 - 1.5 * IQR, 1)
        upper_bound = min(Q3 + 1.5 * IQR, Q3 * 3)
        
        # 剔除异常值
        sub_text_write("整体趋势（剔除异常值）")
        df_no_outliers = df[(df[duration_col] >= lower_bound) & (df[duration_col] <= upper_bound)]
        st.dataframe(df_no_outliers, use_container_width=True,hide_index=True)
        # 按日期重新统计
        date_stats_no_outliers = df_no_outliers.groupby("操作日期")[duration_col].agg(["mean", "median"]).reset_index()
        date_stats_no_outliers.columns = ["操作日期", "平均审核时长(分钟)", "中位数审核时长(分钟)"]
        
        # 绘制剔除异常值后的趋势图
        fig_line_no_outliers = px.line(date_stats_no_outliers, x="操作日期", y=["平均审核时长(分钟)", "中位数审核时长(分钟)"], 
                                      title="审核时长变化趋势（剔除异常值）",
                                      labels={"value": "审核时长(分钟)", "操作日期": "日期", "variable": "统计量"})
        fig_line_no_outliers.update_xaxes(tickformat="%Y-%m-%d")
        st.plotly_chart(fig_line_no_outliers, use_container_width=True)
    else:
        st.write("数据中只有一个操作日期，无法进行时间趋势分析")
    
    
    # 6. 异常值检测
    text_write("6. 异常案件检测")
    # 使用IQR方法检测异常值，动态计算上下界
    Q1 = df[duration_col].quantile(0.25)
    Q3 = df[duration_col].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = max(Q1 - 1.5 * IQR, 1)  # 确保最小审核时长不小于1分钟
    upper_bound = min(Q3 + 1.5 * IQR, Q3 * 3)  # 使用Q3的3倍作为上界的最大值
    
    outliers = df[(df[duration_col] < lower_bound) | (df[duration_col] > upper_bound)]
    if not outliers.empty:
        text_new(f"检测到 {len(outliers)} 个异常案件（审核时长过长或过短的案件）：",15,'#343642')
        st.dataframe(outliers, use_container_width=True)
        
        # 绘制散点图，突出显示异常值
        df['是否异常'] = df[duration_col].apply(lambda x: '异常值' if (x < lower_bound) or (x > upper_bound) else '正常值')
        df['异常类型'] = df[duration_col].apply(lambda x: '审核时长过长' if x > upper_bound else ('审核时长过短' if x < lower_bound else '正常'))
        sub_text_write("审核时长异常值检测")
        # 统计异常类型分布
        outlier_type_dist = df['异常类型'].value_counts()
        st.write("异常类型分布：")
        fig_scatter = px.scatter(df, x=df.index, y=duration_col, color='是否异常',
                                title="",
                                labels={duration_col: "审核时长(分钟)", "index": "案件序号"})
        st.plotly_chart(fig_scatter, use_container_width=True)

        # 对异常案件进行深入分析        
        # 获取异常案件的详细信息
        outlier_cases = outliers['案件id'].tolist()
        outlier_cases_str = "','".join(outlier_cases)
        
        # 查询异常案件的状态变更历史
        SQL_OUTLIER_ANALYSIS = f"""
        SELECT 
            ice.claim_id,
            ice.status,
            ice.operator_name,
            DATE(CONVERT_TZ(ice.create_time, '+00:00', '+08:00')) as operation_date,
            CONVERT_TZ(ice.create_time, '+00:00', '+08:00') as operation_time
        FROM 
            insurance_claim_event ice
        WHERE 
            ice.claim_id IN ('{outlier_cases_str}')
            and CONVERT_TZ(ice.create_time, '+00:00', '+08:00') >= '{start_time}'
            and CONVERT_TZ(ice.create_time, '+00:00', '+08:00') <= '{end_time}'
        ORDER BY 
            ice.claim_id, ice.create_time
        """
        df_outlier_details = CONNECTOR.query(SQL_OUTLIER_ANALYSIS, show_spinner="分析中...", ttl=10)
        df_outlier_details_nhb = CONNECTOR_NHB.query(SQL_OUTLIER_ANALYSIS, show_spinner="分析中...", ttl=10)
        df_outlier_details_ghb = CONNECTOR_GHB.query(SQL_OUTLIER_ANALYSIS, show_spinner="分析中...", ttl=10)
        df_outlier_details_yz = CONNECTOR_YZ.query(SQL_OUTLIER_ANALYSIS, show_spinner="分析中...", ttl=10)

        df_outlier_details = pd.concat([df_outlier_details, df_outlier_details_nhb], ignore_index=True)
        df_outlier_details = pd.concat([df_outlier_details, df_outlier_details_ghb], ignore_index=True)
        df_outlier_details = pd.concat([df_outlier_details, df_outlier_details_yz], ignore_index=True)
        df_outlier_details['claim_id'] = df_outlier_details['claim_id'].astype(str)
        
        # 分析长时间异常案件
        long_duration_cases = outliers[outliers[duration_col] > upper_bound]
        
        if not long_duration_cases.empty:
            sub_text_write("审核时长过长案件分析")
            
            # 分析案件往复情况
            long_cases_details = df_outlier_details[df_outlier_details['claim_id'].isin(long_duration_cases['案件id'])]
            long_cases_details = long_cases_details.sort_values(['claim_id', 'operation_time'])
            
            # 统计每个案件中每个状态的出现次数
            status_counts = long_cases_details.groupby(['claim_id', 'status']).size().reset_index(name='状态出现次数')
            # 找出每个案件中出现次数大于1的状态
            repeated_status = status_counts[status_counts['状态出现次数'] > 1].groupby('claim_id').size().reset_index(name='往复状态数')
            # 根据是否存在重复状态判断往复
            status_changes = repeated_status.merge(right=long_cases_details[['claim_id']].drop_duplicates(), how='right', on='claim_id')
            status_changes['是否往复'] = status_changes['往复状态数'].apply(lambda x: '存在往复' if pd.notnull(x) else '不存在往复')
            recursive_stats = status_changes['是否往复'].value_counts()
            
            # 创建饼图展示往复情况
            fig = go.Figure(data=[go.Pie(
                labels=recursive_stats.index,
                values=recursive_stats.values,
                hole=0.3
            )])
            fig.update_layout(
                title="长时间案件往复情况分布",
                showlegend=True
            )
            st.plotly_chart(fig, use_container_width=True)
            
            # 展示详细信息
            long_duration_details = long_duration_cases.merge(status_changes, left_on='案件id', right_on='claim_id')
            long_duration_details = long_duration_details[['案件id', '审核时长(分钟)', '状态','是否往复']]
            st.dataframe(long_duration_details.sort_values('审核时长(分钟)', ascending=False), use_container_width=True,hide_index=True)
        
        # 分析短时间异常案件
        short_duration_cases = outliers[outliers[duration_col] < lower_bound]
        if not short_duration_cases.empty:
            sub_text_write("\n审核时长过短案件分析")
            
            # 分析操作人员类型
            short_cases_details = df_outlier_details[df_outlier_details['claim_id'].isin(short_duration_cases['案件id'])]
            # 判断是否为自动审核（操作人员包含'admin'）
            short_cases_details['是否自动审核'] = short_cases_details['operator_name'].apply(
                lambda x: '自动审核' if 'admin' in str(x).lower() else '正常审核'
            )
            # 只要案件中有一条记录是admin操作的就标记为自动审核
            auto_audit_stats = short_cases_details.groupby('claim_id')['是否自动审核'].agg(
                lambda x: '自动审核' if any(audit == '自动审核' for audit in x) else '正常审核'
            ).value_counts()
            
            # 创建饼图展示自动审核情况
            fig = go.Figure(data=[go.Pie(
                labels=auto_audit_stats.index,
                values=auto_audit_stats.values,
                hole=0.3
            )])
            fig.update_layout(
                title="短时间案件自动审核情况分布",
                showlegend=True
            )
            st.plotly_chart(fig, use_container_width=True)
            
            # 展示详细信息
            short_duration_details = short_duration_cases.copy()
            # 确保short_cases_details中存在'是否自动审核'列
            if '是否自动审核' in short_cases_details.columns:
                auto_audit_map = short_cases_details.groupby('claim_id')['是否自动审核'].apply(
                    lambda x: '自动审核' if '自动审核' in x.values else '正常审核'
                )
                short_duration_details['是否自动审核'] = short_duration_details['案件id'].map(auto_audit_map)
            else:
                short_duration_details['是否自动审核'] = '正常审核'
            short_duration_details = short_duration_details[['案件id', '审核时长(分钟)', '状态', '是否自动审核']]
            st.dataframe(short_duration_details.sort_values('审核时长(分钟)'), use_container_width=True,hide_index=True)
    
    # 7. 综合分析结论
    text_write("7. 综合分析结论")
    avg_duration = df[duration_col].mean()
    median_duration = df[duration_col].median()
    max_duration = df[duration_col].max()
    min_duration = df[duration_col].min()
    sub_text_write(f"概述：")
    text_new(f"   平均审核时长为 {avg_duration:.2f} 分钟，中位数为 {median_duration:.2f} 分钟",15,'#343642')
    text_new(f"   最长审核时长为 {max_duration:.2f} 分钟，最短审核时长为 {min_duration:.2f} 分钟",15,'#343642')
    
    # 按状态分析
    sub_text_write(f"案件状态分析：")
    for status, group in df.groupby("状态"):
        group_avg = group[duration_col].mean()
        group_median = group[duration_col].median()
        text_new(f"   {status}案件的平均审核时长为 {group_avg:.2f} 分钟，中位数为 {group_median:.2f} 分钟",15,'#343642')


def main():
    st.subheader("案件审核时长分析")
    is_iframe, user_permission, disable, product_serial_code_iframe, product_set_code_iframe = agent_auth_check()
    product_info = get_product_set_code()
    if product_set_code_iframe in product_info['product_set_code'].values or product_set_code_iframe is None:
        if is_iframe == 1 and product_set_code_iframe is not None:
            iframe_product_set_name = \
            product_info[product_info['product_set_code'] == product_set_code_iframe][
                'product_set_code'].values[0]
            product_set_name = st.multiselect('产品集', product_info['product_set_name'].tolist(),
                                                 default=[iframe_product_set_name],disabled=True)
        else:
            jiangsu_yhb_products = product_info[product_info['product_serial_code'] == 'jiangsu_yhb']
            product_set_name = st.multiselect('产品集', product_info['product_set_name'].tolist(),
                                                 default=jiangsu_yhb_products['product_set_name'].tolist())
        
        if product_set_name:
            product_info_input = product_info[product_info['product_set_name'].isin(product_set_name)]
            product_set_code = "','".join(product_info_input['product_set_code'].tolist())
            product_set_name = product_info_input['product_set_name'].tolist()
            product_set_code_list = product_info_input['product_set_code'].tolist()
        else:
            product_set_code = "','".join(product_info['product_set_code'].tolist())
            product_set_name = product_info['product_set_name'].tolist()
            product_set_code_list = product_info['product_set_code'].tolist()
        cols = st.columns([0.4, 0.4])
            # 选择日期
        sale_from = datetime.datetime.now() - datetime.timedelta(days=7)
        sale_until = datetime.date.today()

        with cols[0]:
            start_date = st.date_input('开始日期', min_value='2000-01-01', max_value=sale_until, value=sale_from,
                                        key='start_date')

        with cols[1]:
            end_date = st.date_input('结束日期', min_value='2000-01-01', max_value=sale_until, value=sale_until,
                                        key='end_date')
    st.divider()
    if start_date:
        start_time = start_date.strftime('%Y-%m-%d 00:00:00')
    if end_date:
        end_time = end_date.strftime('%Y-%m-%d 23:59:59')

    if start_date > end_date:
        st.error('开始日期不能大于结束日期')
    else:
        with st.spinner('数据加载中...'):
            df_case_statis = get_case_statis(product_set_code,start_time,end_time)
            df_case_statis = df_case_statis[df_case_statis['产品集编码'].isin(product_set_name)]
            
            # 显示原始数据
            text_write("原始数据")
            st.dataframe(df_case_statis, use_container_width=True,hide_index=True)
            
            # 进行全面的统计分析
            analyze_audit_duration(df_case_statis,start_time,end_time)

    st.divider()
    text_write("操作轨迹查询")
    tracking_data_layout()



if __name__ == "__main__":
    main()
