### 1、配置

参考.streamlit/secret.example.toml文件，创建 secret.toml文件，填写数据库连接以及相关信息.  
**注意如果部署在阿里云内网中，需要将host地址改为内网地址，否则会连接失败**

### 2、运行

设置环境变量 DISABLE_AUTH=true，可以关闭登录验证， 用于调试

```shell
streamlit run 首页.py

# 关闭校验
export DISABLE_AUTH=true streamlit run 首页.py
```


### 3、其他

- 数据库连接地址，**按照实际情况填写host地址**，本地使用地址，会因为ip端口变动，导致连接失败

- 如果使用maxkey登录后显示无法登陆的提示，尝试重新登录，本次根据maxkey应用的client_id、client_secret以及日期
  生成token，可以保持应用的唯一性（由于子程序无法每次获得用户名称，所以用时间进行替代————streamlit的session无法持久化
  ，用户名称无法保存）

- streamlit 隐藏侧边栏引用的网页源码class类名，会根据streamlit版本不同，有所不同

### 4、数据调用说明
【腾讯文档】数据生产说明
https://docs.qq.com/aio/DS0hVYUNabXRialVO