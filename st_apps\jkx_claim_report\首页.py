import logging.config
import os
import traceback
from datetime import datetime
import streamlit as st

from utils.auth import Auth, maxkey_get_person_info, iframe_get_person_info,auth_from_session,get_agent_auth,agent_auth_check

st.set_page_config(
    page_title="理赔数据报表",
    layout="wide",
    initial_sidebar_state="expanded",
    page_icon="📊"
)

LOG_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'log')
if not os.path.exists(LOG_PATH):
    os.makedirs(LOG_PATH)
LOG_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(threadName)s] [%(levelname)s] (%(pathname)s:%(lineno)d %(funcName)s) %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'standard',
        },
        'app_file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'INFO',
            'formatter': 'standard',
            'filename': os.path.join(LOG_PATH, 'app.log'),
            'maxBytes': 1024 * 1024 * 100,  # 100MB
            'backupCount': 5,
        },
        'error_file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'ERROR',
            'formatter': 'standard',
            'filename': os.path.join(LOG_PATH, 'error.log'),
            'maxBytes': 1024 * 1024 * 100,  # 100MB
            'backupCount': 5,
        },
    },
    'loggers': {
        '': {  # Root logger
            'handlers': ['console', 'app_file', 'error_file'],
            'level': 'INFO',
            'propagate': True,
        },
    }
}

logging.config.dictConfig(LOG_CONFIG)
logger = logging.getLogger(__name__)


def check_auth():
    auth = Auth()
    # maxkey 认证
    code = st.query_params.get('code')
    if code is not None:
        person_info = maxkey_get_person_info(code)
        if person_info:
            auth.set_auth_to_cookies(person_info, is_iframe=False)
            return person_info
    # 从iframe中获取鉴权信息
    iframe_code = st.query_params.get('iframecode')
    if iframe_code is not None:
        # 需要前端回传ProductSetCode,displayName、UserId、IframeCode,is_iframe鉴权码等信息
        person_info = iframe_get_person_info(iframe_code)
        auth.set_auth_to_cookies(person_info, is_iframe=True)
        return person_info
    # 尝试从cookie中获取鉴权信息
    person_info = auth.auth_from_cookies()
    if person_info:
        return person_info

    return None


def run(person_info=None):
    # 清除url中的参数
    st.query_params.clear()
    def home_page():
        st.subheader(f"健康险理赔数据报表系统，欢迎使用!")
        st.write('当前用户:', person_info.get('displayName') if person_info else '未知用户')
        st.write('现在时间:', datetime.now())
    from page.理赔报表 import main as claim_report
    from page.ocr识别报告 import main as ocr_report
    from page.审核时效报表 import main as audit_time_report
    from page.案件审核时长分析 import main as case_audit_time_report
    from page.审核人员工作量统计 import main as audit_workload_report
    from page.在途案件审核时效统计 import main as in_transit_audit_time_report
    from page.理赔明细报表 import main as claim_detail_report
    # 查看信息中是否有iframe标识，如果有则使用iframe方式，否则使用默认方式
    if person_info.get('is_iframe') ==1:
        _, _, _, product_serial_code_iframe, _ = agent_auth_check()
        # 看是否有权限查看报表
        if isinstance(person_info['Role'], list):
            roles = set(person_info['Role'])
        else:
            raise TypeError("角色类型错误")

        if any(role in roles for role in {'admin', 'initial_auditor', 'reviewer'}):
            if person_info.get('displayName')[:3] in ['孙召敏','史泽云','金双复']: 
                if product_serial_code_iframe in ['neimenggu_hmb', 'xiantao_xhb', 'shanxi_jkb', 'hunan_amb']:
                    pg = st.navigation(
                        [st.Page(claim_report, title='理赔报表', icon='📊', url_path='/理赔报表'),
                        st.Page(ocr_report, title='OCR识别报告', icon='📊', url_path='/ocr识别报告'),
                        st.Page(audit_time_report, title='审核时效报表', icon='📊', url_path='/审核时效报表'),
                        st.Page(audit_workload_report, title='审核人员工作量统计', icon='🗒', url_path='/审核人员工作量统计'),
                        st.Page(claim_detail_report, title='理赔明细报表', icon='🗒', url_path='/理赔明细报表'),
                        ])
                else:
                    pg = st.navigation(
                        [st.Page(claim_report, title='理赔报表', icon='📊', url_path='/理赔报表'),
                        st.Page(ocr_report, title='OCR识别报告', icon='📊', url_path='/ocr识别报告'),
                        st.Page(audit_time_report, title='审核时效报表', icon='📊', url_path='/审核时效报表'),
                        st.Page(audit_workload_report, title='审核人员工作量统计', icon='🗒', url_path='/审核人员工作量统计'),
                        ])
            else:
                if product_serial_code_iframe in ['neimenggu_hmb', 'xiantao_xhb', 'shanxi_jkb', 'hunan_amb']:
                    pg = st.navigation(
                        [st.Page(claim_report, title='理赔报表', icon='📊', url_path='/理赔报表'),
                        st.Page(audit_time_report, title='审核时效报表', icon='📊', url_path='/审核时效报表'),
                        st.Page(audit_workload_report, title='审核人员工作量统计', icon='🗒', url_path='/审核人员工作量统计'),
                        st.Page(claim_detail_report, title='理赔明细报表', icon='🗒', url_path='/理赔明细报表'),
                        ])
                else:
                    pg = st.navigation(
                        [st.Page(claim_report, title='理赔报表', icon='📊', url_path='/理赔报表'),
                        st.Page(audit_time_report, title='审核时效报表', icon='📊', url_path='/审核时效报表'),
                        st.Page(audit_workload_report, title='审核人员工作量统计', icon='🗒', url_path='/审核人员工作量统计'),
                        ])
            pg.run()
        else:
            st.error('您没有权限访问此页面,请联系管理员！')
    else:
        if person_info.get('displayName') in ['孙召敏', '史泽云','马杰','金双','王瑞','常博','孙艳']:
            pg = st.navigation(
                {"首页": [st.Page(home_page, title='首页', icon='🏠'), ],
                    "理赔": [st.Page(claim_report, title='理赔报表', icon='📊',url_path ='/理赔报表'), 
                             st.Page(claim_detail_report, title='理赔明细报表', icon='🗒', url_path='/理赔明细报表'),
                            ],
                    "审核": [ st.Page(audit_time_report, title='审核时效报表', icon='📊', url_path='/审核时效报表'),
                              st.Page(case_audit_time_report, title='案件审核时长分析', icon='📊', url_path='/案件审核时长分析'),
                              st.Page(audit_workload_report, title='审核人员工作量统计', icon='🗒', url_path='/审核人员工作量统计'),
                              st.Page(in_transit_audit_time_report, title='在途案件审核时效统计', icon='🗒', url_path='/在途案件审核时效统计'),
                             ],
                    "OCR": [st.Page(ocr_report, title='OCR识别报告', icon='📊',url_path ='/ocr识别报告'),
                             ],
                })
        elif person_info.get('displayName') in ['yx001','yx002']:
            pg = st.navigation(
                {"理赔": [st.Page(claim_report, title='理赔报表', icon='📊',url_path ='/理赔报表'), 
                             st.Page(claim_detail_report, title='理赔明细报表', icon='🗒', url_path='/理赔明细报表'),
                             ],
                })
        else:
            pg = st.navigation(
                {"首页": [st.Page(home_page, title='首页', icon='🏠'), ],
                    "理赔": [st.Page(claim_report, title='理赔报表', icon='📊',url_path ='/理赔报表'),
                             st.Page(claim_detail_report, title='理赔明细报表', icon='🗒', url_path='/理赔明细报表'), ],
                    "审核": [ st.Page(audit_time_report, title='审核时效报表', icon='📊', url_path='/审核时效报表'),
                              st.Page(audit_workload_report, title='审核人员工作量统计', icon='🗒', url_path='/审核人员工作量统计'),
                              st.Page(in_transit_audit_time_report, title='在途案件审核时效统计', icon='🗒', url_path='/在途案件审核时效统计'),
                             ],
                })
        pg.run()



def auth_failed():
    
    st.error(f'请重新登陆')


def main():
    # 判断环境变量 DISABLE_AUTH 是否存在，如果存在则不进行认证
    if 'DISABLE_AUTH' in os.environ:
        logger.info('DISABLE_AUTH is set, skip auth')
        run()

    else:
        try:
            person_info = check_auth()
            if person_info:
                run(person_info)
            else:
                auth_failed()
        except Exception as e:
            traceback.print_exc()
            st.error(f'认证失败: {e}')


if __name__ == "__main__":
    main()
