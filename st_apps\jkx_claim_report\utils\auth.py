import copy
import json
import logging
import os
import time
from datetime import datetime
from urllib.parse import quote

import jwt
import requests
import streamlit as st
from utils.utils import send_feishu_message
from st_cookies_manager import <PERSON><PERSON><PERSON>anager
from streamlit_autorefresh import st_autorefresh

from utils.st import hide_sidebar
from utils.utils import send_feishu_message

logger = logging.getLogger(__name__)

COOKIE_NAME = 'JKX_CLAIM_REPORT_AUTH'
JWT_SECRET = 'tde!zkh@QFA4w264yre'  # 历史其他项目的JWT_SECRET tde!zkh@QFA4wxh4yre
JWT_EXPIRE = 3600 * 24 * 7  # 7天有效期


def maxkey_get_person_info(code):
    logger.info(f'maxkey_get_person_info {code} ...')

    maxkey = st.secrets["maxkey"]

    MAXKEY_CLIENT_ID = maxkey.client_id
    MAXKEY_CLIENT_SECRET = maxkey.client_secret
    MAXKEY_REDIRECT_URL = quote(maxkey.redirect_uri)

    MAXKEY_TOKEN_URL = "https://maxkey.njzhyl.cn/sign/authz/oauth/v20/token"
    MAXKEY_USER_INFO_URL = "https://maxkey.njzhyl.cn/sign/api/oauth/v20/me"

    # 构建获取token的URL
    token_url = f"{MAXKEY_TOKEN_URL}?client_id={MAXKEY_CLIENT_ID}&client_secret={MAXKEY_CLIENT_SECRET}&redirect_uri={MAXKEY_REDIRECT_URL}&code={code}&grant_type=authorization_code"
    response = requests.get(token_url)
    logger.info(f"maxkey_get_person_info response: {response.text}")
    response_json = response.json()
    if 'error' in response_json:
        raise Exception(f"统一登陆获取token失败: {response_json}")

    access_token = response.json()['access_token']
    if access_token:
        get_person = f"{MAXKEY_USER_INFO_URL}?access_token=" + access_token
        response_person = requests.get(get_person)
        person_info = response_person.json()
        person_info = json.loads(person_info)
        print(person_info)
        return person_info


def iframe_get_person_info(iframecode):
    """
    获取iframe嵌入时，获取用户信息
    :param iframecode: 用于验证的code
    :return:
    """
    person_info = jwt.decode(iframecode, JWT_SECRET, algorithms=['HS256'])
    # code 为日期
    if person_info['IframeCode'] == datetime.now().strftime('%Y-%m-%d'):
        return person_info
    else:
        raise Exception(f"登陆失败")


def get_agent_auth(username, product_serial_code):
    """
    获取人员的信息，需要鉴权，是否给予权限
    主承保有全部权限
    :param user_id:
    :return:
    """
    # 添加两个个角色，可以查看报表通用版本、内部版本，如果有内部版本权限，则放开，如果有内部权限的版本则只能看内部版本
    # 如果校验没有权限，则提示错误信息
    CONNECTOR_JKX = st.connection('claim', type='sql')
    sql = """
    SELECT
        u.id,u.username,u.fullname,r.code
    FROM
        `user` u
        LEFT JOIN user_role ur ON u.id = ur.user_id
        left join role r on ur.role_id = r.id
        LEFT JOIN app a ON ur.app_id = a.id
        LEFT JOIN app_product_serial aps ON a.id = aps.app_id 
    WHERE
        u.username = '{username}' 
        AND aps.product_serial_code = '{product_serial_code}' 
        AND u.delete_time IS NULL
    """
    df_user_record = CONNECTOR_JKX.query(sql.format(username=username, product_serial_code=product_serial_code), ttl=0)

    # 如果有管理员权限，则赋值内部版本权限，否则如果有外部版本权限，则赋值外部版本权限，否则赋值0，外部程序需要提示没有权限
    if 'admin' in df_user_record['code'].values.tolist() or 'reviewer' in df_user_record['code'].values.tolist():
        df_user_record['permission'] = 1
    elif 'initial_auditor' in df_user_record['code'].values.tolist():
        df_user_record['permission'] = 2
    else:
        df_user_record['permission'] = 0
    return df_user_record


def get_person_info():
    """
    获取session中的person_info，如果初始获取为None，则尝试刷新页面最多两次
    :return: person_info 或者 None 如果重试后仍然无法获取
    """
    retry_count = 0
    max_retries = 2
    while retry_count < max_retries:
        person_info = auth_from_session()
        if person_info is not None:
            return person_info  # 如果成功获取到person_info，则直接返回
        retry_count += 1
        if retry_count <= max_retries:
            st_autorefresh()  # 在重试之前刷新页面
    return None  # 如果达到最大重试次数仍未获取到信息，则返回None


def agent_auth_check():
    """
    保司权限校验
    :return:是否iframe过来的数据，是否有权限，是否禁用筛选条件，产品系列编码
    """
    is_iframe = 0
    user_permission = 1
    product_serial_code_iframe = None
    product_set_code_iframe = None
    disable = False
    try:
        person_info = get_person_info()
        is_iframe = person_info.get('is_iframe')
        if is_iframe == 1:
            user_id = person_info['UserId']
            product_serial_code_iframe = person_info['ProductSerialCode']
            product_set_code_iframe = person_info['ProductSetCode']
            auth_agent_user = get_agent_auth(user_id, product_serial_code_iframe)
            # 根据权限判定是否禁用，后期看是否要放开
            user_permission = auth_agent_user['permission'].values[0]
            # 从管理端来的，全部禁用筛选条件
            disable = True
    except Exception as e:
        pass
    return is_iframe, user_permission, disable, product_serial_code_iframe, product_set_code_iframe


def auth_from_session():
    """
    从cookies中获取用户信息
    """
    cookies_dict = st.session_state
    # 从字典中获取Cookie字符串
    cookie_str = cookies_dict.get('CookieManager.sync_cookies', '')
    # 分割cookie字符串为列表
    cookies_list = cookie_str.split('; ')

    # 遍历列表以找到INSURE_REPORT_AUTH
    for cookie in cookies_list:
        if cookie.startswith(COOKIE_NAME):
            # 分割cookie以获取值
            cookie_value = cookie.split('=')[1]
            person_info = jwt.decode(cookie_value, JWT_SECRET, algorithms=['HS256'])
            # 转换为 datetime 对象
            dt_object = datetime.fromtimestamp(person_info['exp'])
            #  日期与系统日期对比，判断是否过期
            if dt_object < datetime.now():
                return None
            return person_info
    # 如果没有找到INSURE_REPORT_AUTH IFRAME_COOKIE_NAME
    print("JKX_CLAIM_REPORT_AUTH cookie not found.")
    return None


class Auth:
    # 初始化，避免设置全局变量，导致任何地方都可以访问
    def __init__(self):
        self.cookie_manager = CookieManager()
        if not self.cookie_manager.ready():
            st.spinner()
            st.stop()

    def auth_from_cookies(self):
        """
        从cookies中获取用户信息
        """
        cookie_value = self.cookie_manager.get(COOKIE_NAME)
        # 未登陆
        if cookie_value is None:
            return None

        # 尝试按照JWT解码
        try:
            person_info = jwt.decode(cookie_value, JWT_SECRET, algorithms=['HS256'])
            # 转换为 datetime 对象
            dt_object = datetime.fromtimestamp(person_info['exp'])
            #  日期与系统日期对比，判断是否过期
            if dt_object < datetime.now():
                return None
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None

        return person_info

    def set_auth_to_cookies(self, person_info, is_iframe=False):
        """
        根据用户信息，生成jwt，并写入cookies
        """
        payload = copy.copy(person_info)
        payload['exp'] = time.time() + JWT_EXPIRE
        if is_iframe:
            token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')
            self.cookie_manager[COOKIE_NAME] = token
        else:
            payload['is_iframe'] = 0
            token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')
            self.cookie_manager[COOKIE_NAME] = token
        self.cookie_manager.save()
        st_autorefresh()  # 刷新页面，获取最新的session_state
        logger.info(f'set_auth_to_cookies finish')


def auth_required(func):
    def wrapper(*args, **kwargs):
        if 'DISABLE_AUTH' in os.environ:
            return func(*args, **kwargs)

        auth = Auth()
        person_info = auth.auth_from_cookies()
        if person_info is None:
            hide_sidebar()
            st.error('请重新登陆')
            return
        return func(*args, **kwargs)

    return wrapper
